import cv2
import time
import wiringpi
from square_detector import SquareDetector
from utils import draw_detection_results

def main():
    """
    主函数：运动目标控制与自动追踪系统
    功能：摄像头采集、正方形检测、GPIO按键控制标定
    注意：程序运行时不保存任何图像文件，支持外接显示屏显示
    """
    # ========== GPIO按键配置 (暂时注释) ==========
    # 初始化WiringPi库和GPIO配置
    print("初始化WiringPi库...")
    wiringpi.wiringPiSetup()  # 使用wPi引脚编号

    # 配置GPIO2_D4按键引脚 (wPi 13, 物理引脚22)
    BUTTON_PIN = 13  # GPIO2_D4对应的wPi引脚号
    wiringpi.pinMode(BUTTON_PIN, wiringpi.INPUT)

    # 按键配置：下拉电阻 + 高电平触发
    wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)  # 下拉电阻

    print(f"GPIO配置完成 - 按键引脚: wPi {BUTTON_PIN} (物理引脚22)")
    print("按键配置: 下拉电阻，按键一端接物理引脚22，另一端接3.3V")
    print("按键逻辑: 未按下=低电平(0)，按下=高电平(1)")
    # ===============================================

    # 初始化摄像头并输出调试信息
    try:
        import sys
        import os

        # 检查显示环境
        display_available = os.environ.get('DISPLAY') is not None
        print(f"显示环境检查: DISPLAY={os.environ.get('DISPLAY', 'None')}")

        if not display_available:
            print("警告：未检测到显示环境，请确保：")
            print("1. 如果使用SSH，请使用 'ssh -X' 或 'ssh -Y' 连接")
            print("2. 如果使用外接显示屏，请直接在OrangePi上运行程序")
            print("3. 或者使用 main_headless.py 进行无头模式运行")

        print("尝试打开摄像头...")
        # 根据操作系统选择后端，优化外接显示屏兼容性
        if sys.platform.startswith('win'):
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(0, cv2.CAP_V4L2)

        if not cap.isOpened():
            print("无法打开摄像头。请检查是否被其他程序占用，或尝试更换VideoCapture参数。")
            print(f"系统平台: {sys.platform}")
            print("尝试使用不同的 VideoCapture 参数或检查摄像头连接。")
            return

        print("摄像头已打开。")
        print(f"摄像头分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")

        # 初始化正方形检测器
        square_detector = SquareDetector()

        # 标定状态变量
        calibration_done = False

        # 按键状态说明（暂时禁用GPIO按键）：
        # 当前使用键盘按键：'c'键进行标定，'r'键重置，'q'键退出

        print("系统就绪！")
        print("🔧 当前模式：键盘控制模式 (GPIO按键已禁用)")
        print("📋 操作说明：")
        print("   - 按 'c' 键：进行矩形标定")
        print("   - 按 'p' 键：进行矩形标定 (终端输入)")
        print("   - 按 'r' 键：重置标定")
        print("   - 按 'q' 键：退出程序")
        print("🎯 识别目标：黑色边框、白色内部的矩形，中心有黑色原点")
        print("⚠️  重要：只有按下'c'键时才会进行矩形检测，其余时候不进行任何检测操作")
        print("📁 注意：程序运行过程中不会保存任何图像文件")

        frame_count = 0  # 帧计数器，用于调试

        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                print("无法读取摄像头画面，ret:", ret, ", frame:", frame)
                break

            frame_count += 1

            # ========== 按键检测逻辑 (暂时禁用) ==========
            # 🚫 暂时禁用GPIO按键检测，改为键盘按键触发
            # current_button_state = wiringpi.digitalRead(BUTTON_PIN)
            # current_time = time.time()

            # 使用键盘按键代替GPIO按键进行测试
            key = cv2.waitKey(1) & 0xFF

            if (key == ord('c') or key == ord('p')) and not calibration_done:  # 按'c'或'p'键进行标定
                print("=" * 60)
                key_name = "'c'键" if key == ord('c') else "'p'键"
                print(f"[帧 {frame_count}] 键盘触发标定 (按下了{key_name})")
                print("⚠️  注意：只有现在才会进行矩形检测！")
                print("正在分析当前帧...")

                # 🔥 关键：只有在这里才进行矩形检测和标定
                calibration_result = square_detector.calibrate_square(frame)

                if calibration_result:
                    calibration_done = True
                    print("✅ 矩形标定成功！")

                    # 输出标定结果详情
                    calibrated_data = square_detector.get_calibrated_data()
                    print(f"矩形边界: {calibrated_data['square_bounds']}")
                    print(f"中心点坐标: {calibrated_data['center_point']}")
                    print(f"矩形顶点: {calibrated_data['square_vertices']}")
                else:
                    print("❌ 矩形标定失败")
                    print("请确保画面中有：")
                    print("  1. 黑色边框的矩形")
                    print("  2. 矩形内部为白色")
                    print("  3. 矩形中心有黑色原点")
                    print("  4. 矩形大小适中且完整可见")

                print("=" * 60)

            elif key == ord('r'):  # 按'r'键重置标定
                square_detector.reset_calibration()
                calibration_done = False
                print("🔄 标定已重置")

            # ===============================================

            # 🔥 关键：这里只是绘制已有的检测结果，不进行新的检测
            # draw_detection_results 函数只使用已标定的数据，不会调用任何检测方法
            display_frame = draw_detection_results(frame, square_detector, calibration_done)

            # 添加帧计数和操作提示
            cv2.putText(display_frame, f"Frame: {frame_count}", (10, display_frame.shape[0] - 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(display_frame, "Press 'c'/'p' to calibrate, 'r' to reset, 'q' to quit",
                       (10, display_frame.shape[0] - 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 在外接显示屏上显示画面
            try:
                cv2.imshow('Motion Target Control & Auto Tracking System', display_frame)
            except cv2.error as e:
                if "GTK" in str(e):
                    print("显示错误：无法初始化GTK后端")
                    print("解决方案：")
                    print("1. 如果使用SSH，请重新连接：ssh -X orangepi@ip地址")
                    print("2. 如果使用外接显示屏，请直接在OrangePi桌面运行")
                    print("3. 或使用无头模式：sudo python3 main_headless.py")
                    break
                else:
                    raise e

            # 退出检查已经在上面的按键处理中完成了
            if key == ord('q'):
                break

    except Exception as e:
        import traceback
        print("程序发生异常：", e)
        traceback.print_exc()
    finally:
        try:
            cap.release()
        except Exception:
            pass
        cv2.destroyAllWindows()
        print("程序已退出")

if __name__ == '__main__':
    main()
