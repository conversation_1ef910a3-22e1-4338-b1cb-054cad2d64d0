import cv2
import threading
import time
import wiringpi
from square_detector import SquareDetector
from utils import draw_detection_results

def main():
    """
    主函数：运动目标控制与自动追踪系统
    功能：摄像头采集、正方形检测、GPIO按键控制标定
    """
    # 初始化WiringPi库和GPIO配置
    print("初始化WiringPi库...")
    wiringpi.wiringPiSetup()  # 使用wPi引脚编号

    # 配置GPIO2_D4按键引脚 (wPi 13, 物理引脚22)
    BUTTON_PIN = 13  # GPIO2_D4对应的wPi引脚号
    wiringpi.pinMode(BUTTON_PIN, wiringpi.INPUT)
    wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)  # 配置下拉电阻
    print(f"GPIO配置完成 - 按键引脚: wPi {BUTTON_PIN} (物理引脚22)")
    print("接线说明: 按键一端接物理引脚22，另一端接GND(如物理引脚6)")

    # 初始化摄像头并输出调试信息
    try:
        import sys
        print("尝试打开摄像头...")
        # 根据操作系统选择后端
        if sys.platform.startswith('win'):
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
        if not cap.isOpened():
            print("无法打开摄像头。请检查是否被其他程序占用，或尝试更换VideoCapture参数。")
            print(f"系统平台: {sys.platform}")
            print("尝试使用不同的 VideoCapture 参数或检查摄像头连接。")
            return
        print("摄像头已打开。")
        print(f"摄像头分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")

        # 初始化正方形检测器
        square_detector = SquareDetector()

        # 按键状态变量
        last_button_state = False
        calibration_done = False
        button_press_time = 0
        DEBOUNCE_DELAY = 0.2  # 200ms消抖延时

        print("系统就绪！按下按键进行矩形标定，按'q'退出程序")
        print("识别目标：黑色边框、白色内部的矩形，中心有黑色原点")

        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                print("无法读取摄像头画面，ret:", ret, ", frame:", frame)
                break

            # 检测按键状态 (高电平有效)
            current_button_state = wiringpi.digitalRead(BUTTON_PIN)
            current_time = time.time()

            # 检测按键按下事件 (上升沿触发) + 硬件消抖
            if current_button_state and not last_button_state:
                button_press_time = current_time
                print("检测到按键按下，等待消抖...")

            # 按键消抖处理：按键保持按下状态超过消抖时间才认为有效
            elif current_button_state and last_button_state:
                if (current_time - button_press_time) >= DEBOUNCE_DELAY and not calibration_done:
                    print("按键消抖完成，开始矩形标定...")
                    calibration_result = square_detector.calibrate_square(frame)
                    if calibration_result:
                        calibration_done = True
                        print("✅ 矩形标定成功！")
                    else:
                        print("❌ 矩形标定失败，请确保画面中有黑色边框、白色内部的矩形")
                    # 设置一个标志避免重复标定
                    button_press_time = 0

            last_button_state = current_button_state

            # 绘制检测结果
            frame = draw_detection_results(frame, square_detector, calibration_done)

            # 显示画面
            cv2.imshow('Motion Target Control & Auto Tracking System', frame)

            # 按q退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    except Exception as e:
        import traceback
        print("程序发生异常：", e)
        traceback.print_exc()
    finally:
        try:
            cap.release()
        except Exception:
            pass
        cv2.destroyAllWindows()
        print("程序已退出")

if __name__ == '__main__':
    main()
