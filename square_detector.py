"""
正方形检测器模块
功能：检测浅黑色正方形和黑色中心点，支持标定和实时显示
作者：运动目标控制与自动追踪系统
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List


class SquareDetector:
    """
    正方形检测器类
    负责检测浅黑色正方形和黑色中心点，支持一次性标定和持续显示
    """

    def __init__(self):
        """
        初始化正方形检测器
        """
        # 标定状态
        self.is_calibrated = False

        # 标定后的正方形信息
        self.calibrated_square = None  # 正方形四个顶点坐标
        self.calibrated_center = None  # 中心点坐标
        self.square_bounds = None      # 正方形边界框 (x, y, w, h)

        # 颜色检测参数 (HSV色彩空间) - 基于实际图像分析优化
        # 黑色边框线的HSV范围 (浅黑色到深黑色)
        self.frame_hsv_lower = np.array([0, 0, 0])       # 下限 - 黑色边框
        self.frame_hsv_upper = np.array([180, 255, 80])  # 上限 - 黑色边框 (提高V值)

        # 白色内部区域的HSV范围 (亮白色区域)
        self.inner_hsv_lower = np.array([0, 0, 180])     # 下限 - 白色内部
        self.inner_hsv_upper = np.array([180, 50, 255])  # 上限 - 白色内部

        # 黑色中心点的HSV范围 (深色点)
        self.center_hsv_lower = np.array([0, 0, 0])      # 下限
        self.center_hsv_upper = np.array([180, 255, 60]) # 上限 (稍微提高)

        # 检测参数 - 支持多种分辨率的图像 (包括1920x1080)
        self.min_square_area = 3000      # 最小矩形面积
        self.max_square_area = 1000000   # 最大矩形面积 (支持超高分辨率)
        self.min_center_area = 10        # 最小中心点面积
        self.max_center_area = 5000      # 最大中心点面积
        self.square_approx_epsilon = 0.02 # 多边形逼近精度

        # 新增：检测质量参数
        self.min_contour_perimeter = 200  # 最小轮廓周长
        self.max_contour_perimeter = 10000 # 最大轮廓周长 (支持高分辨率)

    def set_frame_color_range(self, hsv_lower: np.ndarray, hsv_upper: np.ndarray):
        """
        设置矩形边框颜色检测范围
        :param hsv_lower: HSV下限 [H, S, V]
        :param hsv_upper: HSV上限 [H, S, V]
        """
        self.frame_hsv_lower = hsv_lower
        self.frame_hsv_upper = hsv_upper
        print(f"矩形边框颜色范围已更新: 下限{hsv_lower}, 上限{hsv_upper}")

    def set_inner_color_range(self, hsv_lower: np.ndarray, hsv_upper: np.ndarray):
        """
        设置矩形内部颜色检测范围
        :param hsv_lower: HSV下限 [H, S, V]
        :param hsv_upper: HSV上限 [H, S, V]
        """
        self.inner_hsv_lower = hsv_lower
        self.inner_hsv_upper = hsv_upper
        print(f"矩形内部颜色范围已更新: 下限{hsv_lower}, 上限{hsv_upper}")

    def set_center_color_range(self, hsv_lower: np.ndarray, hsv_upper: np.ndarray):
        """
        设置中心点颜色检测范围
        :param hsv_lower: HSV下限 [H, S, V]
        :param hsv_upper: HSV上限 [H, S, V]
        """
        self.center_hsv_lower = hsv_lower
        self.center_hsv_upper = hsv_upper
        print(f"中心点颜色范围已更新: 下限{hsv_lower}, 上限{hsv_upper}")

    def detect_rectangle_contours(self, frame: np.ndarray) -> List[np.ndarray]:
        """
        改进的矩形轮廓检测算法
        结合边缘检测和颜色分割，提高检测准确性
        :param frame: 输入图像
        :return: 检测到的矩形轮廓列表
        """
        print("开始矩形检测...")

        # 方法1：边缘检测
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 使用自适应阈值提高边缘检测效果
        edges = cv2.Canny(gray, 30, 100, apertureSize=3)

        # 形态学操作连接断开的边缘
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        edges = cv2.morphologyEx(edges, cv2.MORPH_DILATE, kernel, iterations=1)

        # 找到轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"边缘检测找到 {len(contours)} 个轮廓")

        # 方法2：颜色分割辅助验证
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        black_mask = cv2.inRange(hsv, self.frame_hsv_lower, self.frame_hsv_upper)

        # 筛选矩形轮廓
        rectangle_contours = []
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            print(f"轮廓 {i}: 面积={area:.0f}, 周长={perimeter:.0f}")

            # 面积和周长筛选
            if (self.min_square_area <= area <= self.max_square_area and
                self.min_contour_perimeter <= perimeter <= self.max_contour_perimeter):

                # 多边形逼近
                epsilon = self.square_approx_epsilon * perimeter
                approx = cv2.approxPolyDP(contour, epsilon, True)

                print(f"轮廓 {i}: 逼近后顶点数={len(approx)}")

                # 检查是否为四边形
                if len(approx) == 4:
                    # 检查长宽比
                    x, y, w, h = cv2.boundingRect(approx)
                    aspect_ratio = float(w) / h

                    print(f"轮廓 {i}: 边界=({x},{y},{w},{h}), 长宽比={aspect_ratio:.2f}")

                    if 0.3 <= aspect_ratio <= 3.0:  # 放宽长宽比限制
                        # 验证轮廓的凸性
                        if cv2.isContourConvex(approx):
                            # 验证内部是否为白色
                            if self._verify_white_interior(frame, approx):
                                # 验证边框是否为黑色
                                if self._verify_black_border(black_mask, approx):
                                    rectangle_contours.append(approx)
                                    print(f"✅ 找到有效矩形: 面积={area:.0f}, 边界=({x},{y},{w},{h}), 长宽比={aspect_ratio:.2f}")
                                else:
                                    print(f"❌ 边框不是黑色，跳过")
                            else:
                                print(f"❌ 矩形内部不是白色，跳过")
                        else:
                            print(f"❌ 轮廓不是凸形，跳过")
                    else:
                        print(f"❌ 长宽比不合适: {aspect_ratio:.2f}")
                else:
                    print(f"❌ 不是四边形，顶点数: {len(approx)}")
            else:
                print(f"❌ 面积或周长不符合要求")

        print(f"最终找到 {len(rectangle_contours)} 个有效矩形")
        return rectangle_contours

    def _verify_white_interior(self, frame: np.ndarray, contour: np.ndarray) -> bool:
        """
        验证矩形内部是否为白色
        :param frame: 输入图像
        :param contour: 矩形轮廓
        :return: 是否为白色内部
        """
        # 创建掩码
        mask = np.zeros(frame.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [contour], 255)

        # 获取矩形内部区域的HSV值
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

        # 在掩码区域内应用颜色范围检测
        white_mask = cv2.inRange(hsv, self.inner_hsv_lower, self.inner_hsv_upper)

        # 计算矩形内部白色像素的比例
        interior_mask = cv2.bitwise_and(mask, white_mask)
        interior_white_pixels = np.sum(interior_mask > 0)
        total_interior_pixels = np.sum(mask > 0)

        if total_interior_pixels == 0:
            return False

        white_ratio = interior_white_pixels / total_interior_pixels
        print(f"矩形内部白色像素比例: {white_ratio:.2f}")
        return white_ratio > 0.6  # 60%以上为白色认为有效（降低要求）

    def _verify_black_border(self, black_mask: np.ndarray, contour: np.ndarray) -> bool:
        """
        验证矩形边框是否为黑色
        :param black_mask: 黑色区域掩码
        :param contour: 矩形轮廓
        :return: 是否为黑色边框
        """
        # 创建轮廓掩码
        contour_mask = np.zeros(black_mask.shape, dtype=np.uint8)
        cv2.drawContours(contour_mask, [contour], -1, 255, 8)  # 绘制较粗的轮廓

        # 计算轮廓区域内黑色像素的比例
        border_black_pixels = np.sum(cv2.bitwise_and(black_mask, contour_mask) > 0)
        total_border_pixels = np.sum(contour_mask > 0)

        if total_border_pixels == 0:
            return False

        black_ratio = border_black_pixels / total_border_pixels
        print(f"边框黑色像素比例: {black_ratio:.2f}")
        return black_ratio > 0.3  # 30%以上为黑色认为有效

    def detect_center_point(self, frame: np.ndarray, square_bounds: Tuple[int, int, int, int]) -> Optional[Tuple[int, int]]:
        """
        在正方形区域内检测黑色中心点
        :param frame: 输入图像
        :param square_bounds: 正方形边界框 (x, y, w, h)
        :return: 中心点坐标 (x, y) 或 None
        """
        x, y, w, h = square_bounds

        # 提取正方形区域
        roi = frame[y:y+h, x:x+w]
        if roi.size == 0:
            return None

        # 转换到HSV色彩空间
        hsv_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

        # 创建中心点颜色掩码
        center_mask = cv2.inRange(hsv_roi, self.center_hsv_lower, self.center_hsv_upper)

        # 形态学操作
        kernel = np.ones((2, 2), np.uint8)
        center_mask = cv2.morphologyEx(center_mask, cv2.MORPH_OPEN, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(center_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 寻找最大的中心点轮廓
        best_center = None
        max_area = 0

        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_center_area < area < self.max_center_area and area > max_area:
                # 计算质心
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"]) + x  # 转换到全图坐标
                    cy = int(M["m01"] / M["m00"]) + y
                    best_center = (cx, cy)
                    max_area = area

        return best_center

    def calibrate_square(self, frame: np.ndarray) -> bool:
        """
        标定矩形和中心点
        :param frame: 当前帧图像
        :return: 标定是否成功
        """
        print("开始矩形标定...")

        # 检测矩形轮廓
        rectangle_contours = self.detect_rectangle_contours(frame)

        if not rectangle_contours:
            print("未检测到符合条件的矩形（黑色边框，白色内部）")
            return False

        # 选择面积最大的矩形
        best_rectangle = max(rectangle_contours, key=cv2.contourArea)

        # 获取矩形边界框
        x, y, w, h = cv2.boundingRect(best_rectangle)
        self.square_bounds = (x, y, w, h)

        # 保存矩形顶点
        self.calibrated_square = best_rectangle.reshape(-1, 2)

        # 检测中心点
        center_point = self.detect_center_point(frame, self.square_bounds)

        if center_point is None:
            print("在矩形内未检测到黑色中心点")
            # 如果没有检测到中心点，使用几何中心
            center_x = x + w // 2
            center_y = y + h // 2
            self.calibrated_center = (center_x, center_y)
            print(f"使用几何中心作为中心点: ({center_x}, {center_y})")
        else:
            self.calibrated_center = center_point
            print(f"检测到黑色中心点: {center_point}")

        # 标定完成
        self.is_calibrated = True

        print(f"矩形标定完成:")
        print(f"  - 矩形边界: {self.square_bounds}")
        print(f"  - 中心点坐标: {self.calibrated_center}")
        print(f"  - 矩形顶点: {self.calibrated_square}")

        return True

    def get_calibrated_data(self) -> dict:
        """
        获取标定后的数据
        :return: 包含正方形和中心点信息的字典
        """
        if not self.is_calibrated:
            return {}

        return {
            'square_vertices': self.calibrated_square,
            'center_point': self.calibrated_center,
            'square_bounds': self.square_bounds,
            'is_calibrated': self.is_calibrated
        }

    def reset_calibration(self):
        """
        重置标定状态
        """
        self.is_calibrated = False
        self.calibrated_square = None
        self.calibrated_center = None
        self.square_bounds = None
        print("标定状态已重置")