#!/bin/bash

# 运动目标控制与自动追踪系统启动脚本
# 用于解决显示环境问题

echo "=== 运动目标控制与自动追踪系统启动脚本 ==="
echo ""

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "错误：请以root权限运行此脚本"
    echo "使用方法: sudo ./run_with_display.sh"
    exit 1
fi

# 检查显示环境
echo "1. 检查显示环境..."
if [ -z "$DISPLAY" ]; then
    echo "   警告：未设置DISPLAY环境变量"
    
    # 尝试设置本地显示
    echo "   尝试设置本地显示环境..."
    export DISPLAY=:0.0
    echo "   已设置 DISPLAY=:0.0"
else
    echo "   显示环境: $DISPLAY"
fi

# 检查X11权限并修复
echo ""
echo "2. 检查并修复X11权限..."
if command -v xhost >/dev/null 2>&1; then
    echo "   配置X11权限..."

    # 获取原始用户
    ORIGINAL_USER=${SUDO_USER:-orangepi}
    echo "   原始用户: $ORIGINAL_USER"

    # 复制X11认证文件
    if [ -f "/home/<USER>/.Xauthority" ]; then
        echo "   复制X11认证文件..."
        cp "/home/<USER>/.Xauthority" "/root/.Xauthority" 2>/dev/null
        chown root:root "/root/.Xauthority" 2>/dev/null
        echo "   ✓ X11认证文件已复制"
    else
        echo "   警告：未找到X11认证文件"
    fi

    # 设置xhost权限（通过原始用户执行）
    su - $ORIGINAL_USER -c "DISPLAY=:0 xhost +local:root" 2>/dev/null && echo "   ✓ xhost权限已设置" || echo "   警告：xhost设置失败"

else
    echo "   警告：xhost命令不可用"
fi

# 检查摄像头设备
echo ""
echo "3. 检查摄像头设备..."
if [ -e /dev/video0 ]; then
    echo "   摄像头设备: /dev/video0 ✓"
else
    echo "   警告：未找到摄像头设备 /dev/video0"
fi

# 检查Python依赖
echo ""
echo "4. 检查Python依赖..."
python3 -c "import cv2, wiringpi; print('   依赖库检查通过 ✓')" 2>/dev/null || {
    echo "   错误：Python依赖库缺失"
    exit 1
}

echo ""
echo "5. 启动程序..."
echo "   如果出现显示问题，请尝试以下解决方案："
echo "   - 方案1: 直接在OrangePi桌面运行（推荐）"
echo "   - 方案2: SSH连接时使用 'ssh -X' 或 'ssh -Y'"
echo "   - 方案3: 使用无头模式 'sudo python3 main_headless.py'"
echo ""

# 启动主程序
python3 main.py
