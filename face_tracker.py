
import cv2
import mediapipe as mp
import os
import urllib.request

def ensure_mediapipe_model():
    """
    检查并自动下载mediapipe人脸检测模型文件，保证兼容性和可移植性。
    """
    # 获取mediapipe包内模型文件路径
    import mediapipe
    model_dir = os.path.join(os.path.dirname(mediapipe.__file__), 'modules', 'face_detection')
    model_path = os.path.join(model_dir, 'face_detection_short_range_cpu.binarypb')
    if not os.path.exists(model_path):
        print(f"模型文件缺失，自动下载到: {model_path}")
        os.makedirs(model_dir, exist_ok=True)
        url = 'https://storage.googleapis.com/mediapipe-assets/face_detection_short_range_cpu.binarypb'
        try:
            urllib.request.urlretrieve(url, model_path)
            print("模型文件下载完成。")
        except Exception as e:
            print("模型文件下载失败：", e)
            raise

class FaceTracker:
    """
    基于MediaPipe的人脸检测与追踪封装类。
    可扩展支持其他开源方案（如dlib、opencv自带检测器等）。
    """
    def __init__(self, method='mediapipe'):
        self.method = method
        if method == 'mediapipe':
            ensure_mediapipe_model()
            self.mp_face_detection = mp.solutions.face_detection
            self.face_detection = self.mp_face_detection.FaceDetection(model_selection=0, min_detection_confidence=0.5)
        # 预留其他方法接口

    def detect(self, frame):
        """
        检测并返回人脸框列表，每个框为(x, y, w, h)
        """
        results = []
        if self.method == 'mediapipe':
            rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            detections = self.face_detection.process(rgb)
            if detections.detections:
                h, w, _ = frame.shape
                for det in detections.detections:
                    bbox = det.location_data.relative_bounding_box
                    x = int(bbox.xmin * w)
                    y = int(bbox.ymin * h)
                    bw = int(bbox.width * w)
                    bh = int(bbox.height * h)
                    results.append((x, y, bw, bh))
        # 预留其他方法实现
        return results

    def release(self):
        if self.method == 'mediapipe':
            self.face_detection.close()
