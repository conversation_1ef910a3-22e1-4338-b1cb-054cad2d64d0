#!/usr/bin/env python3
"""
创建测试图片
生成包含浅黑色正方形和黑色中心点的测试图像
"""

import cv2
import numpy as np

def create_test_image():
    """
    创建测试图像：白色背景上的黑色边框矩形（内部白色）和黑色中心点
    """
    # 创建白色背景图像 (640x480)
    img = np.ones((480, 640, 3), dtype=np.uint8) * 255

    # 矩形参数
    rect_width = 120   # 矩形宽度
    rect_height = 100  # 矩形高度
    center_x, center_y = 320, 240  # 图像中心
    frame_thickness = 4  # 边框厚度

    # 计算矩形顶点
    half_width = rect_width // 2
    half_height = rect_height // 2
    rect_top_left = (center_x - half_width, center_y - half_height)
    rect_bottom_right = (center_x + half_width, center_y + half_height)

    # 绘制黑色边框矩形（空心）
    frame_color = (0, 0, 0)  # BGR格式的黑色边框
    cv2.rectangle(img, rect_top_left, rect_bottom_right, frame_color, frame_thickness)

    # 绘制黑色中心点 (圆形)
    center_radius = 6
    center_color = (0, 0, 0)  # 纯黑色
    cv2.circle(img, (center_x, center_y), center_radius, center_color, -1)

    # 保存图像
    filename = "test_rectangle.jpg"
    cv2.imwrite(filename, img)
    print(f"测试图像已保存: {filename}")
    print(f"图像尺寸: {img.shape[1]}x{img.shape[0]}")
    print(f"矩形位置: {rect_top_left} 到 {rect_bottom_right}")
    print(f"边框颜色 (BGR): {frame_color}")
    print(f"边框厚度: {frame_thickness}px")
    print(f"中心点位置: ({center_x}, {center_y})")
    print(f"中心点颜色 (BGR): {center_color}")

    # 显示HSV值信息
    hsv_img = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    frame_hsv = hsv_img[center_y - half_height, center_x]  # 边框的HSV值
    inner_hsv = hsv_img[center_y, center_x - 20]  # 内部白色区域的HSV值
    center_hsv = hsv_img[center_y, center_x]  # 中心点的HSV值

    print(f"边框HSV值: {frame_hsv}")
    print(f"内部白色HSV值: {inner_hsv}")
    print(f"中心点HSV值: {center_hsv}")

    return filename

def create_realistic_test_image():
    """
    创建更真实的测试图像：带有一些噪声和不完美边缘的黑色边框矩形
    """
    # 创建稍微带噪声的背景
    img = np.random.randint(240, 256, (480, 640, 3), dtype=np.uint8)

    # 矩形参数
    rect_width = 110
    rect_height = 90
    center_x, center_y = 320, 240
    frame_thickness = 3

    # 计算矩形顶点（稍微不规则）
    half_width = rect_width // 2
    half_height = rect_height // 2

    # 绘制稍微不规则的矩形边框
    pts = np.array([
        [center_x - half_width, center_y - half_height],
        [center_x + half_width - 1, center_y - half_height + 1],
        [center_x + half_width + 1, center_y + half_height - 1],
        [center_x - half_width + 1, center_y + half_height + 1]
    ], np.int32)

    # 绘制黑色边框
    frame_color = (0, 0, 0)
    cv2.polylines(img, [pts], True, frame_color, frame_thickness)

    # 黑色中心点（稍微偏移）
    center_offset_x, center_offset_y = center_x + 1, center_y - 1
    cv2.circle(img, (center_offset_x, center_offset_y), 5, (0, 0, 0), -1)

    # 添加一些噪声
    noise = np.random.randint(-8, 9, img.shape, dtype=np.int16)
    img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)

    filename = "test_rectangle_realistic.jpg"
    cv2.imwrite(filename, img)
    print(f"真实测试图像已保存: {filename}")

    return filename

if __name__ == "__main__":
    print("创建测试图像...")
    print("目标：黑色边框、白色内部的矩形，中心有黑色原点")
    create_test_image()
    create_realistic_test_image()
    print("完成！")
