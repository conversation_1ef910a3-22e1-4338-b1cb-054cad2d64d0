import wiringpi
import time

# PWM输出的GPIO引脚
# 16//10
def pwm_x(pwm):
# 初始化wiringpi
    wiringpi.wiringPiSetup()  

    # 设置PWM模式
    wiringpi.pinMode(16, wiringpi.GPIO.PWM_OUTPUT)
    wiringpi.pwmSetMode(16,wiringpi.GPIO.PWM_MODE_MS)  # 使用标记空间模式

    #wiringpi.orangepi_pwm_set_period(16,20000000)
    wiringpi.pwmToneWrite(16, 25) # 设置PWM频率
    wiringpi.pwmSetRange(16,10000)  # 设置PWM范围

    def set_duty(duty):
        duty= int(duty)
        wiringpi.pwmWrite(16, duty)

    try:
        
        # 渐亮渐暗循环
        set_duty(750)#250-750-1250
        while True:
            time.sleep(1)
    finally:
        set_duty(0)  # 将PWM设为0sc
    








 