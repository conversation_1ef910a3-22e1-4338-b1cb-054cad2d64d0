import wiringpi
import time

# PWM输出的GPIO引脚
# 16//10

def pwm_set():
    wiringpi.wiringPiSetup()  

    # 设置PWM模式
    wiringpi.pinMode(16, wiringpi.GPIO.PWM_OUTPUT)
    wiringpi.pwmSetMode(16,wiringpi.GPIO.PWM_MODE_MS)  # 使用标记空间模式

    #wiringpi.orangepi_pwm_set_period(16,20000000)
    wiringpi.pwmToneWrite(16, 25) # 设置PWM频率
    wiringpi.pwmSetRange(16,10000)  # 设置PWM范围


    wiringpi.wiringPiSetup()  

    # 设置PWM模式
    wiringpi.pinMode(10, wiringpi.GPIO.PWM_OUTPUT)
    wiringpi.pwmSetMode(10,wiringpi.GPIO.PWM_MODE_MS)  # 使用标记空间模式

    #wiringpi.orangepi_pwm_set_period(16,20000000)
    wiringpi.pwmToneWrite(10, 25) # 设置PWM频率
    wiringpi.pwmSetRange(10,10000)  # 设置PWM范围


def pwm_x(pwm):#250-750-1250
# 初始化wiringpi
    

    
    pwm= int(pwm)
    wiringpi.pwmWrite(16, pwm)

    
def pwm_y(pwm):#250-750-1250
# 初始化wiringpi
    

    
    pwm= int(pwm)
    wiringpi.pwmWrite(10, pwm)
