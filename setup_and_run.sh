#!/bin/bash

# 两步式启动脚本：先设置权限，再以root身份运行
# 使用方法：./setup_and_run.sh

echo "=== 运动目标控制与自动追踪系统 - 两步启动 ==="
echo ""

# 检查当前用户
CURRENT_USER=$(whoami)
echo "当前用户: $CURRENT_USER"

if [ "$CURRENT_USER" = "root" ]; then
    echo "错误：请不要以root身份运行此脚本"
    echo "正确使用方法："
    echo "  ./setup_and_run.sh"
    echo ""
    echo "或者手动执行："
    echo "  xhost +local:root"
    echo "  sudo python3 main.py"
    exit 1
fi

echo ""
echo "第一步：设置X11权限..."
export DISPLAY=:0
xhost +local:root
if [ $? -eq 0 ]; then
    echo "✓ X11权限设置成功"
else
    echo "✗ X11权限设置失败"
    echo "请确保您在图形界面环境中运行此脚本"
    exit 1
fi

echo ""
echo "第二步：以root身份启动程序..."
echo "即将启动程序，请输入sudo密码："

# 以root身份运行主程序
sudo python3 main.py

echo ""
echo "程序已退出"
