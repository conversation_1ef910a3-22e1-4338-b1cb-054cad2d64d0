#!/usr/bin/env python3
"""
显示环境测试脚本
用于检测OpenCV显示功能是否正常工作
"""

import cv2
import numpy as np
import os
import sys

def test_display_environment():
    """测试显示环境是否可用"""
    print("=== 显示环境测试 ===")
    print()
    
    # 检查DISPLAY环境变量
    display = os.environ.get('DISPLAY')
    print(f"1. DISPLAY环境变量: {display if display else '未设置'}")
    
    # 检查平台
    print(f"2. 系统平台: {sys.platform}")
    
    # 检查OpenCV版本
    print(f"3. OpenCV版本: {cv2.__version__}")
    
    # 测试创建窗口
    print("4. 测试创建显示窗口...")
    
    try:
        # 创建一个简单的测试图像
        test_image = np.zeros((300, 400, 3), dtype=np.uint8)
        test_image[:] = (50, 100, 150)  # 深蓝色背景
        
        # 添加文字
        cv2.putText(test_image, "Display Test", (50, 150), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(test_image, "Press 'q' to quit", (50, 200), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)
        
        # 尝试显示窗口
        cv2.imshow('Display Test', test_image)
        print("   ✓ 窗口创建成功")
        print("   按 'q' 键退出测试")
        
        # 等待按键
        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
        
        cv2.destroyAllWindows()
        print("   ✓ 显示测试完成")
        return True
        
    except cv2.error as e:
        print(f"   ✗ 显示测试失败: {e}")
        print()
        print("解决方案:")
        print("1. 如果使用SSH，请使用: ssh -X 或 ssh -Y")
        print("2. 如果使用外接显示屏，请直接在OrangePi桌面运行")
        print("3. 或者使用无头模式程序")
        return False
    
    except Exception as e:
        print(f"   ✗ 未知错误: {e}")
        return False

def test_camera():
    """测试摄像头是否可用"""
    print()
    print("=== 摄像头测试 ===")
    
    try:
        # 尝试打开摄像头
        cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
        
        if not cap.isOpened():
            print("   ✗ 无法打开摄像头")
            return False
        
        # 获取摄像头信息
        width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
        print(f"   ✓ 摄像头已打开，分辨率: {width}x{height}")
        
        # 读取一帧测试
        ret, frame = cap.read()
        if ret:
            print("   ✓ 摄像头读取正常")
        else:
            print("   ✗ 摄像头读取失败")
        
        cap.release()
        return ret
        
    except Exception as e:
        print(f"   ✗ 摄像头测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("运动目标控制与自动追踪系统 - 环境测试")
    print("=" * 50)
    
    # 测试显示环境
    display_ok = test_display_environment()
    
    # 测试摄像头
    camera_ok = test_camera()
    
    print()
    print("=== 测试结果 ===")
    print(f"显示环境: {'✓ 正常' if display_ok else '✗ 异常'}")
    print(f"摄像头: {'✓ 正常' if camera_ok else '✗ 异常'}")
    
    if display_ok and camera_ok:
        print()
        print("✓ 环境测试通过，可以运行主程序:")
        print("  sudo python3 main.py")
    else:
        print()
        print("✗ 环境测试未通过，建议:")
        if not display_ok:
            print("  - 显示问题：使用 sudo python3 main_headless.py")
        if not camera_ok:
            print("  - 摄像头问题：检查摄像头连接")

if __name__ == '__main__':
    main()
