#!/usr/bin/env python3
"""
测试'p'键矩形标定功能
使用标准测试图像验证检测算法
"""

import cv2
import time
from square_detector import SquareDetector
from utils import draw_detection_results

def test_p_key_calibration():
    """测试'p'键标定功能"""
    print("=== 测试'p'键矩形标定功能 ===")
    print("这个测试将使用标准测试图像模拟摄像头输入")
    print("按'p'键进行标定，按'q'键退出")
    
    # 加载测试图像
    test_image = cv2.imread('test_basic_rectangle.jpg')
    if test_image is None:
        print("❌ 无法加载测试图像，请先运行 create_improved_test_images.py")
        return
    
    print("✅ 测试图像加载成功")
    
    # 初始化检测器
    detector = SquareDetector()
    calibration_done = False
    frame_count = 0
    
    print("\n操作说明:")
    print("- 按 'p' 键：进行矩形标定")
    print("- 按 'r' 键：重置标定")
    print("- 按 'q' 键：退出")
    print("\n开始测试...")
    
    while True:
        frame_count += 1
        frame = test_image.copy()  # 使用测试图像作为当前帧
        
        # 检测按键
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('p') and not calibration_done:  # 按'p'键进行标定
            print("=" * 60)
            print(f"[帧 {frame_count}] 'p'键触发标定")
            print("⚠️  注意：只有现在才会进行矩形检测！")
            print("正在分析当前帧...")
            
            # 进行矩形检测和标定
            start_time = time.time()
            calibration_result = detector.calibrate_square(frame)
            end_time = time.time()
            
            if calibration_result:
                calibration_done = True
                print("✅ 矩形标定成功！")
                print(f"⏱️  标定耗时: {end_time - start_time:.3f}秒")
                
                # 输出标定结果详情
                calibrated_data = detector.get_calibrated_data()
                print(f"📐 矩形边界: {calibrated_data['square_bounds']}")
                print(f"🎯 中心点坐标: {calibrated_data['center_point']}")
                print(f"📍 矩形顶点: {calibrated_data['square_vertices']}")
            else:
                print("❌ 矩形标定失败")
                print("请确保画面中有：")
                print("  1. 黑色边框的矩形")
                print("  2. 矩形内部为白色")
                print("  3. 矩形中心有黑色原点")
                print("  4. 矩形大小适中且完整可见")
            
            print("=" * 60)
            
        elif key == ord('r'):  # 按'r'键重置标定
            detector.reset_calibration()
            calibration_done = False
            print("🔄 标定已重置")
            
        elif key == ord('q'):  # 退出
            break
        
        # 绘制检测结果
        display_frame = draw_detection_results(frame, detector, calibration_done)
        
        # 添加帧计数和操作提示
        cv2.putText(display_frame, f"Frame: {frame_count}", (10, display_frame.shape[0] - 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(display_frame, f"Frame: {frame_count}", (10, display_frame.shape[0] - 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
        
        cv2.putText(display_frame, "Press 'p' to calibrate, 'r' to reset, 'q' to quit", 
                   (10, display_frame.shape[0] - 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        cv2.putText(display_frame, "Press 'p' to calibrate, 'r' to reset, 'q' to quit", 
                   (10, display_frame.shape[0] - 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        # 添加标定状态显示
        status_text = "Calibrated" if calibration_done else "Not Calibrated"
        status_color = (0, 255, 0) if calibration_done else (0, 0, 255)
        cv2.putText(display_frame, f"Status: {status_text}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        
        # 显示画面
        try:
            cv2.imshow("Test 'p' Key Calibration", display_frame)
        except cv2.error as e:
            print(f"显示错误: {e}")
            print("继续运行但无法显示...")
            time.sleep(0.033)  # 模拟30FPS
    
    cv2.destroyAllWindows()
    print("测试结束")

def main():
    """主函数"""
    try:
        test_p_key_calibration()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
