#!/usr/bin/env python3
"""
PID控制系统测试脚本
用于测试二维云台舵机的PID控制功能
"""

import time
from pid import get_pid_controller, move_laser_to_vertex, set_pid_params, stop_laser_control, reset_pid

def test_pid_basic_functionality():
    """测试PID基本功能"""
    print("=== PID控制系统基本功能测试 ===")
    
    try:
        # 获取PID控制器
        controller = get_pid_controller()
        print("✅ PID控制器获取成功")
        
        # 测试参数设置
        set_pid_params(kp_x=1.5, ki_x=0.2, kd_x=0.08, kp_y=1.5, ki_y=0.2, kd_y=0.08)
        print("✅ PID参数设置成功")
        
        # 获取当前状态
        status = controller.get_current_status()
        print("✅ 状态获取成功")
        print(f"   控制运行状态: {status['control_running']}")
        print(f"   当前PWM值: X={status['current_pwm_x']}, Y={status['current_pwm_y']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_pid_movement_simulation():
    """测试PID移动控制（模拟）"""
    print("\n=== PID移动控制测试（模拟） ===")
    
    try:
        # 模拟不同顶点的误差值
        test_cases = [
            (1, 100, -50, "左下顶点 - 激光点在右上方"),
            (2, 80, 30, "左上顶点 - 激光点在右下方"),
            (3, -120, 60, "右上顶点 - 激光点在左下方"),
            (4, -90, -40, "右下顶点 - 激光点在左上方")
        ]
        
        for vertex_index, error_x, error_y, description in test_cases:
            print(f"\n🎯 测试案例: {description}")
            print(f"   顶点索引: {vertex_index}")
            print(f"   误差值: xx[{vertex_index}]={error_x:+d}px, yy[{vertex_index}]={error_y:+d}px")
            
            # 重置PID状态
            reset_pid()
            
            # 模拟移动（短时间测试）
            controller = get_pid_controller()
            
            # 启动控制循环
            controller.start_control_loop()
            
            # 设置目标
            with controller.control_lock:
                controller.current_error = (error_x, error_y)
            
            # 运行2秒观察控制效果
            print("   开始PID控制...")
            time.sleep(2)
            
            # 获取控制结果
            status = controller.get_current_status()
            print(f"   控制结果: PWM_X={status['current_pwm_x']:.0f}, PWM_Y={status['current_pwm_y']:.0f}")
            
            # 停止控制
            controller.stop_control_loop()
            time.sleep(0.5)
        
        print("\n✅ 移动控制测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 移动控制测试失败: {e}")
        return False

def test_pid_parameter_adjustment():
    """测试PID参数调节"""
    print("\n=== PID参数调节测试 ===")
    
    try:
        # 测试不同的PID参数组合
        parameter_sets = [
            {"name": "保守参数", "kp_x": 0.5, "ki_x": 0.05, "kd_x": 0.02, "kp_y": 0.5, "ki_y": 0.05, "kd_y": 0.02},
            {"name": "标准参数", "kp_x": 1.0, "ki_x": 0.1, "kd_x": 0.05, "kp_y": 1.0, "ki_y": 0.1, "kd_y": 0.05},
            {"name": "激进参数", "kp_x": 2.0, "ki_x": 0.3, "kd_x": 0.1, "kp_y": 2.0, "ki_y": 0.3, "kd_y": 0.1}
        ]
        
        for params in parameter_sets:
            print(f"\n🔧 测试参数组: {params['name']}")
            
            # 设置参数
            set_pid_params(
                kp_x=params['kp_x'], ki_x=params['ki_x'], kd_x=params['kd_x'],
                kp_y=params['kp_y'], ki_y=params['ki_y'], kd_y=params['kd_y']
            )
            
            # 验证参数设置
            controller = get_pid_controller()
            status = controller.get_current_status()
            pid_params = status['pid_parameters']
            
            print(f"   X轴: Kp={pid_params['kp_x']}, Ki={pid_params['ki_x']}, Kd={pid_params['kd_x']}")
            print(f"   Y轴: Kp={pid_params['kp_y']}, Ki={pid_params['ki_y']}, Kd={pid_params['kd_y']}")
        
        print("\n✅ 参数调节测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 参数调节测试失败: {e}")
        return False

def test_pwm_output_range():
    """测试PWM输出范围限制"""
    print("\n=== PWM输出范围测试 ===")
    
    try:
        controller = get_pid_controller()
        
        # 测试极端误差值
        extreme_errors = [
            (1000, 1000, "极大正误差"),
            (-1000, -1000, "极大负误差"),
            (0, 0, "零误差")
        ]
        
        for error_x, error_y, description in extreme_errors:
            print(f"\n🧪 测试: {description} - 误差({error_x}, {error_y})")
            
            # 计算PID输出
            pwm_x, pwm_y = controller.calculate_pid_output(error_x, error_y, 0.02)
            
            # 检查范围限制
            in_range_x = controller.pwm_min <= pwm_x <= controller.pwm_max
            in_range_y = controller.pwm_min <= pwm_y <= controller.pwm_max
            
            print(f"   PWM输出: X={pwm_x:.0f}, Y={pwm_y:.0f}")
            print(f"   范围检查: X={'✅' if in_range_x else '❌'}, Y={'✅' if in_range_y else '❌'}")
            
            if not (in_range_x and in_range_y):
                print(f"   ⚠️  PWM值超出范围 [{controller.pwm_min}-{controller.pwm_max}]")
        
        print("\n✅ PWM输出范围测试完成")
        return True
        
    except Exception as e:
        print(f"❌ PWM输出范围测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("PID控制系统完整测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("基本功能测试", test_pid_basic_functionality()))
    test_results.append(("移动控制测试", test_pid_movement_simulation()))
    test_results.append(("参数调节测试", test_pid_parameter_adjustment()))
    test_results.append(("PWM范围测试", test_pwm_output_range()))
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！PID控制系统准备就绪")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
    
    # 确保停止所有控制
    try:
        stop_laser_control()
    except:
        pass

if __name__ == '__main__':
    main()
