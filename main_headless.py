#!/usr/bin/env python3
"""
运动目标控制与自动追踪系统 - 无头模式
功能：摄像头采集、正方形检测、GPIO按键控制标定（无GUI显示）
适用于SSH远程连接环境
"""

import cv2
import threading
import time
import wiringpi
import numpy as np
from square_detector import SquareDetector

def main():
    """
    主函数：运动目标控制与自动追踪系统（无头模式）
    功能：摄像头采集、正方形检测、GPIO按键控制标定
    """
    # 初始化WiringPi库和GPIO配置
    print("初始化WiringPi库...")
    wiringpi.wiringPiSetup()  # 使用wPi引脚编号
    
    # 配置GPIO2_D4按键引脚 (wPi 13, 物理引脚22)
    BUTTON_PIN = 13  # GPIO2_D4对应的wPi引脚号
    wiringpi.pinMode(BUTTON_PIN, wiringpi.INPUT)
    wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)  # 配置下拉电阻
    print(f"GPIO配置完成 - 按键引脚: wPi {BUTTON_PIN} (物理引脚22)")
    print("接线说明: 按键一端接物理引脚22，另一端接GND(如物理引脚6)")
    
    # 初始化摄像头并输出调试信息
    try:
        import sys
        print("尝试打开摄像头...")
        # 根据操作系统选择后端
        if sys.platform.startswith('win'):
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
        if not cap.isOpened():
            print("无法打开摄像头。请检查是否被其他程序占用，或尝试更换VideoCapture参数。")
            print(f"系统平台: {sys.platform}")
            print("尝试使用不同的 VideoCapture 参数或检查摄像头连接。")
            return
        print("摄像头已打开。")
        print(f"摄像头分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
        
        # 初始化正方形检测器
        square_detector = SquareDetector()
        
        # 按键状态变量
        last_button_state = False
        calibration_done = False
        frame_count = 0
        button_press_time = 0
        DEBOUNCE_DELAY = 0.2  # 200ms消抖延时

        print("系统就绪！按下按键进行矩形标定")
        print("识别目标：黑色边框、白色内部的矩形，中心有黑色原点")
        print("程序运行中... (按Ctrl+C退出)")
        print("=" * 60)

        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                print("无法读取摄像头画面，ret:", ret, ", frame:", frame)
                break

            frame_count += 1
            current_time = time.time()

            # 检测按键状态 (高电平有效)
            current_button_state = wiringpi.digitalRead(BUTTON_PIN)

            # 检测按键按下事件 (上升沿触发) + 硬件消抖
            if current_button_state and not last_button_state:
                button_press_time = current_time
                print(f"\n[帧 {frame_count}] 检测到按键按下，等待消抖...")

            # 按键消抖处理：按键保持按下状态超过消抖时间才认为有效
            elif current_button_state and last_button_state:
                if (current_time - button_press_time) >= DEBOUNCE_DELAY and not calibration_done:
                    print(f"[帧 {frame_count}] 按键消抖完成，开始矩形标定...")

                    # 开始矩形标定（不保存图像文件）

                    calibration_result = square_detector.calibrate_square(frame)
                    if calibration_result:
                        calibration_done = True
                        print("✅ 矩形标定成功！")

                        # 获取标定数据
                        calibrated_data = square_detector.get_calibrated_data()
                        print(f"矩形边界: {calibrated_data['square_bounds']}")
                        print(f"中心点坐标: {calibrated_data['center_point']}")
                        print(f"矩形顶点: {calibrated_data['square_vertices']}")

                        # 标定完成（不保存图像文件）
                        print("标定完成，程序将继续运行（不保存任何图像文件）")

                    else:
                        print("❌ 矩形标定失败，请确保画面中有黑色边框、白色内部的矩形")
                        print("提示：调整矩形颜色范围参数可能有助于检测")
                        print(f"当前边框HSV范围: {square_detector.frame_hsv_lower} - {square_detector.frame_hsv_upper}")
                        print(f"当前内部HSV范围: {square_detector.inner_hsv_lower} - {square_detector.inner_hsv_upper}")

                    # 设置标志避免重复标定
                    button_press_time = 0

            last_button_state = current_button_state
            
            # 每100帧输出一次状态信息
            if frame_count % 100 == 0:
                status = "已标定" if calibration_done else "等待标定"
                button_status = "按下" if current_button_state else "释放"
                print(f"[帧 {frame_count}] 状态: {status}, 按键: {button_status}")
            
            # 短暂延时避免CPU占用过高
            time.sleep(0.01)
                
    except KeyboardInterrupt:
        print("\n收到退出信号，正在关闭程序...")
    except Exception as e:
        import traceback
        print("程序发生异常：", e)
        traceback.print_exc()
    finally:
        try:
            cap.release()
        except Exception:
            pass
        print("程序已退出")

def draw_calibration_result(frame, calibrated_data):
    """
    在图像上绘制标定结果（用于保存图像文件）
    :param frame: 原始图像
    :param calibrated_data: 标定数据
    :return: 绘制后的图像
    """
    result_frame = frame.copy()
    
    # 绘制正方形边界 (绿色实线)
    if calibrated_data['square_vertices'] is not None:
        vertices = calibrated_data['square_vertices']
        # 绘制正方形轮廓
        cv2.polylines(result_frame, [vertices], True, (0, 255, 0), 3)
        
        # 绘制正方形的四条边界线 (更粗的实线)
        for i in range(4):
            pt1 = tuple(vertices[i])
            pt2 = tuple(vertices[(i + 1) % 4])
            cv2.line(result_frame, pt1, pt2, (0, 255, 0), 4)
    
    # 绘制中心点 (红色圆点和十字)
    if calibrated_data['center_point'] is not None:
        cx, cy = calibrated_data['center_point']
        
        # 绘制红色中心点
        cv2.circle(result_frame, (cx, cy), 6, (0, 0, 255), -1)
        cv2.circle(result_frame, (cx, cy), 8, (0, 0, 255), 2)
        
        # 绘制黑色十字标记
        cross_len = 15
        cv2.line(result_frame, (cx - cross_len, cy), (cx + cross_len, cy), (0, 0, 0), 3)
        cv2.line(result_frame, (cx, cy - cross_len), (cx, cy + cross_len), (0, 0, 0), 3)
        
        # 添加文字标注
        coord_text = f"Center: ({cx}, {cy})"
        cv2.putText(result_frame, coord_text, (cx + 20, cy - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(result_frame, coord_text, (cx + 20, cy - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    # 添加标定完成标记
    status_text = "CALIBRATED - Square & Center Locked"
    cv2.putText(result_frame, status_text, (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return result_frame

if __name__ == '__main__':
    main()
