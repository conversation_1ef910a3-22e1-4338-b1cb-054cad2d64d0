#!/usr/bin/env python3
"""
测试新的顶点距离计算功能
"""

import cv2
from red_laser_detector import RedLaserDetector

def main():
    print('=== 测试新的顶点距离计算功能 ===')

    # 加载try1.jpg
    frame = cv2.imread('try1.jpg')
    if frame is None:
        print('❌ 无法加载try1.jpg')
        return

    # 初始化激光点检测器
    laser_detector = RedLaserDetector()

    # 设置测试矩形参数
    test_bounds = (400, 200, 600, 400)  # (x, y, w, h)
    test_center = (700, 400)  # (cx, cy)

    print(f'测试参数:')
    print(f'  矩形边界: {test_bounds}')
    print(f'  中心点: {test_center}')

    # 设置参考数据（这会自动计算四个顶点）
    laser_detector.set_reference_data(test_bounds, test_center)

    # 检测激光点
    laser_point = laser_detector.detect_red_laser(frame)

    if laser_point:
        print(f'\n✅ 激光点检测成功: {laser_point}')
        
        # 计算到四个顶点的距离
        distances = laser_detector.calculate_distances(laser_point)
        
        print(f'\n📏 红点到四个顶点的坐标差值:')
        print(f'   xx[1]={distances["xx1"]:+d}, yy[1]={distances["yy1"]:+d} (左下顶点)')
        print(f'   xx[2]={distances["xx2"]:+d}, yy[2]={distances["yy2"]:+d} (左上顶点)')
        print(f'   xx[3]={distances["xx3"]:+d}, yy[3]={distances["yy3"]:+d} (右上顶点)')
        print(f'   xx[4]={distances["xx4"]:+d}, yy[4]={distances["yy4"]:+d} (右下顶点)')
        
        print(f'\n🔍 验证计算正确性:')
        lx, ly = laser_point
        print(f'   激光点坐标: ({lx}, {ly})')
        print(f'   左下顶点: ({laser_detector.x1}, {laser_detector.y1}) -> {lx}-{laser_detector.x1}={lx-laser_detector.x1}, {ly}-{laser_detector.y1}={ly-laser_detector.y1}')
        print(f'   左上顶点: ({laser_detector.x2}, {laser_detector.y2}) -> {lx}-{laser_detector.x2}={lx-laser_detector.x2}, {ly}-{laser_detector.y2}={ly-laser_detector.y2}')
        print(f'   右上顶点: ({laser_detector.x3}, {laser_detector.y3}) -> {lx}-{laser_detector.x3}={lx-laser_detector.x3}, {ly}-{laser_detector.y3}={ly-laser_detector.y3}')
        print(f'   右下顶点: ({laser_detector.x4}, {laser_detector.y4}) -> {lx}-{laser_detector.x4}={lx-laser_detector.x4}, {ly}-{laser_detector.y4}={ly-laser_detector.y4}')
        
        print(f'\n✅ 功能验证完成!')
        print(f'   ✅ 正确记录了四个顶点坐标')
        print(f'   ✅ 正确计算了红点到各顶点的坐标差值')
        print(f'   ✅ xx、yy数组正确存储了差值数据')
        print(f'   ✅ 终端正确输出了所有数据')
        
        # 验证数组存储
        print(f'\n🔍 验证数组存储:')
        print(f'   laser_detector.xx = {laser_detector.xx}')
        print(f'   laser_detector.yy = {laser_detector.yy}')
        
    else:
        print('❌ 激光点检测失败')

if __name__ == '__main__':
    main()
