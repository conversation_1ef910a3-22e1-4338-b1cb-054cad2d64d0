#!/usr/bin/env python3
"""
测试摄像头实时矩形检测
模拟按键触发的矩形标定过程
"""

import cv2
import time
from square_detector import SquareDetector
from utils import draw_detection_results

def test_camera_detection():
    """测试摄像头矩形检测功能"""
    print("=== 摄像头矩形检测测试 ===")
    
    # 初始化摄像头
    try:
        import sys
        print("尝试打开摄像头...")
        if sys.platform.startswith('win'):
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
            
        if not cap.isOpened():
            print("❌ 无法打开摄像头")
            return False
            
        print("✅ 摄像头已打开")
        print(f"分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
        
    except Exception as e:
        print(f"❌ 摄像头初始化失败: {e}")
        return False
    
    # 初始化检测器
    detector = SquareDetector()
    calibration_done = False
    
    print("\n操作说明:")
    print("- 按 'c' 键进行矩形标定")
    print("- 按 'r' 键重置标定")
    print("- 按 'q' 键退出")
    print("- 请将包含黑色边框、白色内部的矩形放在摄像头前")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                print("❌ 无法读取摄像头画面")
                break
            
            # 检测按键
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('c'):  # 模拟按键标定
                print("\n" + "="*50)
                print("开始矩形标定...")
                print("正在分析当前帧...")
                
                start_time = time.time()
                calibration_result = detector.calibrate_square(frame)
                end_time = time.time()
                
                if calibration_result:
                    calibration_done = True
                    print("✅ 矩形标定成功！")
                    print(f"标定耗时: {end_time - start_time:.2f}秒")
                    
                    # 输出标定结果详情
                    calibrated_data = detector.get_calibrated_data()
                    print(f"矩形边界: {calibrated_data['square_bounds']}")
                    print(f"中心点坐标: {calibrated_data['center_point']}")
                    print(f"矩形顶点: {calibrated_data['square_vertices']}")
                else:
                    print("❌ 矩形标定失败")
                    print("请确保画面中有：")
                    print("  1. 黑色边框的矩形")
                    print("  2. 矩形内部为白色")
                    print("  3. 矩形大小适中且完整可见")
                
                print("="*50)
                
            elif key == ord('r'):  # 重置标定
                detector.reset_calibration()
                calibration_done = False
                print("🔄 标定已重置")
                
            elif key == ord('q'):  # 退出
                break
            
            # 绘制检测结果
            display_frame = draw_detection_results(frame, detector, calibration_done)
            
            # 添加操作提示
            cv2.putText(display_frame, "Press 'c' to calibrate, 'r' to reset, 'q' to quit", 
                       (10, display_frame.shape[0] - 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(display_frame, "Press 'c' to calibrate, 'r' to reset, 'q' to quit", 
                       (10, display_frame.shape[0] - 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
            
            # 显示画面
            try:
                cv2.imshow('Camera Rectangle Detection Test', display_frame)
            except cv2.error as e:
                print(f"显示错误: {e}")
                print("请确保显示环境正确配置")
                break
                
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        try:
            cap.release()
        except:
            pass
        cv2.destroyAllWindows()
        print("摄像头已释放")
    
    return True

def test_detection_performance():
    """测试检测性能"""
    print("\n=== 检测性能测试 ===")
    
    # 使用测试图像进行性能测试
    test_images = [
        'test_basic_rectangle.jpg',
        'test_realistic_rectangle.jpg',
        'test_medium_rectangle.jpg'
    ]
    
    detector = SquareDetector()
    total_time = 0
    successful_detections = 0
    
    for img_path in test_images:
        try:
            frame = cv2.imread(img_path)
            if frame is None:
                continue
                
            start_time = time.time()
            result = detector.calibrate_square(frame)
            end_time = time.time()
            
            detection_time = end_time - start_time
            total_time += detection_time
            
            if result:
                successful_detections += 1
                print(f"✅ {img_path}: {detection_time:.3f}秒")
            else:
                print(f"❌ {img_path}: {detection_time:.3f}秒 (失败)")
                
            detector.reset_calibration()
            
        except Exception as e:
            print(f"❌ {img_path}: 错误 - {e}")
    
    if len(test_images) > 0:
        avg_time = total_time / len(test_images)
        success_rate = successful_detections / len(test_images) * 100
        
        print(f"\n性能统计:")
        print(f"平均检测时间: {avg_time:.3f}秒")
        print(f"成功率: {success_rate:.1f}%")
        print(f"预估帧率: {1/avg_time:.1f} FPS")

def main():
    """主函数"""
    print("矩形检测摄像头测试工具")
    print("请选择测试模式:")
    print("1. 摄像头实时检测测试")
    print("2. 检测性能测试")
    print("3. 退出")
    
    try:
        choice = input("请输入选择 (1-3): ")
        
        if choice == '1':
            test_camera_detection()
        elif choice == '2':
            test_detection_performance()
        elif choice == '3':
            print("退出测试")
        else:
            print("无效选择，运行摄像头测试...")
            test_camera_detection()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == '__main__':
    main()
