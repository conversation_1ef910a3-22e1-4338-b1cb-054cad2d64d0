#!/usr/bin/env python3
"""
专门测试try.jpg图像的检测效果
调整参数以适应真实图像
"""

import cv2
import numpy as np
from square_detector import SquareDetector
from utils import draw_detection_results

def analyze_try_image():
    """分析try.jpg图像特征"""
    print("=== 分析try.jpg图像特征 ===")
    
    frame = cv2.imread('try.jpg')
    if frame is None:
        print("❌ 无法加载try.jpg")
        return None
    
    print(f"图像尺寸: {frame.shape}")
    
    # 转换到HSV和灰度
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    print(f"灰度值范围: {gray.min()} - {gray.max()}")
    print(f"HSV V通道范围: {hsv[:,:,2].min()} - {hsv[:,:,2].max()}")
    
    # 边缘检测分析
    edges = cv2.Canny(gray, 30, 100)
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    print(f"检测到 {len(contours)} 个轮廓")
    
    # 分析主要轮廓
    large_contours = []
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if area > 1000:  # 只看大轮廓
            perimeter = cv2.arcLength(contour, True)
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            print(f"轮廓 {i}: 面积={area:.0f}, 周长={perimeter:.0f}, 顶点数={len(approx)}")
            
            if len(approx) == 4:
                large_contours.append((contour, area, perimeter))
    
    print(f"找到 {len(large_contours)} 个四边形轮廓")
    return frame, large_contours

def test_with_adjusted_params():
    """使用调整后的参数测试try.jpg"""
    print("\n=== 使用调整后的参数测试 ===")
    
    frame = cv2.imread('try.jpg')
    if frame is None:
        print("❌ 无法加载try.jpg")
        return
    
    # 创建检测器并调整参数
    detector = SquareDetector()
    
    # 针对高分辨率图像调整参数
    detector.min_square_area = 10000      # 提高最小面积
    detector.max_square_area = 1000000    # 大幅提高最大面积
    detector.min_contour_perimeter = 500  # 提高最小周长
    detector.max_contour_perimeter = 15000 # 大幅提高最大周长
    
    # 调整颜色检测范围
    detector.inner_hsv_lower = np.array([0, 0, 150])   # 降低白色检测下限
    detector.inner_hsv_upper = np.array([180, 80, 255]) # 放宽白色检测上限
    
    print("调整后的参数:")
    print(f"面积范围: {detector.min_square_area} - {detector.max_square_area}")
    print(f"周长范围: {detector.min_contour_perimeter} - {detector.max_contour_perimeter}")
    print(f"白色HSV范围: {detector.inner_hsv_lower} - {detector.inner_hsv_upper}")
    
    # 进行检测
    result = detector.calibrate_square(frame)
    
    if result:
        print("✅ 检测成功！")
        data = detector.get_calibrated_data()
        print(f"矩形边界: {data['square_bounds']}")
        print(f"中心点: {data['center_point']}")
        
        # 保存结果
        result_frame = draw_detection_results(frame, detector, True)
        cv2.imwrite('try_detection_result.jpg', result_frame)
        print("结果已保存为 try_detection_result.jpg")
        
        return True
    else:
        print("❌ 检测失败")
        return False

def test_with_preprocessing():
    """使用预处理后测试try.jpg"""
    print("\n=== 使用预处理测试 ===")
    
    frame = cv2.imread('try.jpg')
    if frame is None:
        print("❌ 无法加载try.jpg")
        return
    
    # 预处理：缩放图像
    scale_factor = 0.5  # 缩放到一半
    height, width = frame.shape[:2]
    new_width = int(width * scale_factor)
    new_height = int(height * scale_factor)
    
    resized_frame = cv2.resize(frame, (new_width, new_height))
    print(f"缩放后尺寸: {resized_frame.shape}")
    
    # 保存缩放后的图像
    cv2.imwrite('try_resized.jpg', resized_frame)
    print("缩放后图像已保存为 try_resized.jpg")
    
    # 使用标准参数检测缩放后的图像
    detector = SquareDetector()
    result = detector.calibrate_square(resized_frame)
    
    if result:
        print("✅ 缩放后检测成功！")
        data = detector.get_calibrated_data()
        print(f"矩形边界: {data['square_bounds']}")
        print(f"中心点: {data['center_point']}")
        
        # 保存结果
        result_frame = draw_detection_results(resized_frame, detector, True)
        cv2.imwrite('try_resized_result.jpg', result_frame)
        print("结果已保存为 try_resized_result.jpg")
        
        return True
    else:
        print("❌ 缩放后检测仍然失败")
        return False

def main():
    """主函数"""
    print("try.jpg 图像检测测试工具")
    print("=" * 50)
    
    # 1. 分析图像特征
    result = analyze_try_image()
    if result is None:
        return
    
    # 2. 使用调整参数测试
    success1 = test_with_adjusted_params()
    
    # 3. 使用预处理测试
    success2 = test_with_preprocessing()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"调整参数测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"预处理测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 or success2:
        print("🎉 至少有一种方法成功检测到矩形！")
    else:
        print("⚠️  try.jpg可能不包含标准的黑色边框、白色内部矩形")
        print("建议检查图像内容或使用标准测试图像")

if __name__ == '__main__':
    main()
