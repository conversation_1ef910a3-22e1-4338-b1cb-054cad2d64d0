#!/usr/bin/env python3
"""
GPIO按键测试程序
测试GPIO2_D4按键的读取功能
"""

import wiringpi
import time

def test_gpio_button():
    """
    测试GPIO按键功能
    """
    print("=== GPIO按键测试 ===")
    
    try:
        # 初始化WiringPi
        if wiringpi.wiringPiSetup() == -1:
            print("❌ WiringPi初始化失败，需要root权限")
            return False
            
        print("✅ WiringPi初始化成功")
        
        # 配置按键引脚
        BUTTON_PIN = 13  # GPIO2_D4对应的wPi引脚号
        wiringpi.pinMode(BUTTON_PIN, wiringpi.INPUT)

        # 选择配置方案
        print("请选择按键配置方案:")
        print("1. 下拉电阻 + 按键接3.3V (推荐)")
        print("2. 上拉电阻 + 按键接GND")

        try:
            choice = input("请输入选择 (1或2): ").strip()
        except:
            choice = "1"  # 默认选择1

        if choice == "2":
            # 上拉电阻配置
            wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_UP)
            print(f"✅ 按键引脚配置完成: wPi {BUTTON_PIN} (物理引脚22)")
            print("📌 接线方法: 按键一端接物理引脚22，另一端接GND(如物理引脚6)")
            print("📌 按键逻辑: 未按下=高电平(1)，按下=低电平(0)")
            trigger_level = False  # 低电平触发
        else:
            # 下拉电阻配置 (默认)
            wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)
            print(f"✅ 按键引脚配置完成: wPi {BUTTON_PIN} (物理引脚22)")
            print("📌 接线方法: 按键一端接物理引脚22，另一端接3.3V(如物理引脚1)")
            print("📌 按键逻辑: 未按下=低电平(0)，按下=高电平(1)")
            trigger_level = True   # 高电平触发

        print("按键测试开始，按Ctrl+C退出...")
        print("=" * 50)
        
        # 初始化状态
        if trigger_level:
            last_state = False  # 下拉电阻，初始为低电平
        else:
            last_state = True   # 上拉电阻，初始为高电平

        press_count = 0

        while True:
            # 读取按键状态
            current_state = bool(wiringpi.digitalRead(BUTTON_PIN))

            # 检测按键按下事件
            if trigger_level:
                # 高电平触发：检测上升沿
                if current_state and not last_state:
                    press_count += 1
                    print(f"[{time.strftime('%H:%M:%S')}] 🔴 按键按下！(第{press_count}次) - 高电平触发")
                elif not current_state and last_state:
                    print(f"[{time.strftime('%H:%M:%S')}] 🔵 按键释放 - 回到低电平")
            else:
                # 低电平触发：检测下降沿
                if not current_state and last_state:
                    press_count += 1
                    print(f"[{time.strftime('%H:%M:%S')}] 🔴 按键按下！(第{press_count}次) - 低电平触发")
                elif current_state and not last_state:
                    print(f"[{time.strftime('%H:%M:%S')}] 🔵 按键释放 - 回到高电平")

            last_state = current_state

            # 每5秒输出一次状态
            if int(time.time()) % 5 == 0:
                level_text = "高电平" if current_state else "低电平"
                state_text = "按下" if (current_state == trigger_level) else "释放"
                print(f"[{time.strftime('%H:%M:%S')}] 📊 当前: {level_text} ({state_text}), 总按下次数: {press_count}")
                time.sleep(1)  # 避免重复输出

            time.sleep(0.1)  # 100ms轮询间隔
            
    except KeyboardInterrupt:
        print("\n测试结束")
        return True
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    test_gpio_button()
