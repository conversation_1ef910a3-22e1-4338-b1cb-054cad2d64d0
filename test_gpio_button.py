#!/usr/bin/env python3
"""
GPIO按键测试程序
测试GPIO2_D4按键的读取功能
"""

import wiringpi
import time

def test_gpio_button():
    """
    测试GPIO按键功能
    """
    print("=== GPIO按键测试 ===")
    
    try:
        # 初始化WiringPi
        if wiringpi.wiringPiSetup() == -1:
            print("❌ WiringPi初始化失败，需要root权限")
            return False
            
        print("✅ WiringPi初始化成功")
        
        # 配置按键引脚
        BUTTON_PIN = 13  # GPIO2_D4对应的wPi引脚号
        wiringpi.pinMode(BUTTON_PIN, wiringpi.INPUT)
        wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)
        
        print(f"✅ 按键引脚配置完成: wPi {BUTTON_PIN} (物理引脚22)")
        print("接线说明: 按键一端接物理引脚22，另一端接GND")
        print("按键测试开始，按Ctrl+C退出...")
        print("=" * 50)
        
        last_state = False
        press_count = 0
        
        while True:
            # 读取按键状态
            current_state = wiringpi.digitalRead(BUTTON_PIN)
            
            # 检测按键按下事件 (上升沿)
            if current_state and not last_state:
                press_count += 1
                print(f"[{time.strftime('%H:%M:%S')}] 按键按下！(第{press_count}次)")
                
            # 检测按键释放事件 (下降沿)
            elif not current_state and last_state:
                print(f"[{time.strftime('%H:%M:%S')}] 按键释放")
            
            last_state = current_state
            
            # 每5秒输出一次状态
            if int(time.time()) % 5 == 0:
                state_text = "按下" if current_state else "释放"
                print(f"[{time.strftime('%H:%M:%S')}] 当前状态: {state_text}, 总按下次数: {press_count}")
                time.sleep(1)  # 避免重复输出
            
            time.sleep(0.1)  # 100ms轮询间隔
            
    except KeyboardInterrupt:
        print("\n测试结束")
        return True
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    test_gpio_button()
