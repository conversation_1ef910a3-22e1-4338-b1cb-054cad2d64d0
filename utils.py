import cv2
import numpy as np

def draw_faces(frame, faces, color=(0,255,0), thickness=2):
    """
    在frame上绘制人脸框 (保留原有功能)
    :param frame: 原始图像
    :param faces: 人脸框列表，每个为(x, y, w, h)
    :param color: 框颜色
    :param thickness: 线宽
    """
    for (x, y, w, h) in faces:
        # 计算人脸中心点
        cx = x + w // 2
        cy = y + h // 2
        # 打印中心点坐标
        print(f"人脸中心点坐标: ({cx}, {cy})")
        # 绘制红色中心点
        cv2.circle(frame, (cx, cy), 4, (0, 0, 255), -1)
        # 绘制黑色十字
        cross_len = max(10, min(w, h) // 4)
        cv2.line(frame, (cx - cross_len, cy), (cx + cross_len, cy), (0, 0, 0), 2)
        cv2.line(frame, (cx, cy - cross_len), (cx, cy + cross_len), (0, 0, 0), 2)
    return frame

def draw_detection_results(frame, square_detector, calibration_done):
    """
    绘制正方形检测结果和标定状态
    :param frame: 输入图像
    :param square_detector: 正方形检测器实例
    :param calibration_done: 是否已完成标定
    :return: 绘制后的图像
    """
    # 创建输出图像副本
    output_frame = frame.copy()

    # 如果已标定，绘制标定后的正方形和中心点
    if calibration_done and square_detector.is_calibrated:
        calibrated_data = square_detector.get_calibrated_data()

        # 绘制正方形边界 (绿色实线)
        if calibrated_data['square_vertices'] is not None:
            vertices = calibrated_data['square_vertices']
            # 绘制正方形轮廓
            cv2.polylines(output_frame, [vertices], True, (0, 255, 0), 3)

            # 绘制正方形的四条边界线 (更粗的实线)
            for i in range(4):
                pt1 = tuple(vertices[i])
                pt2 = tuple(vertices[(i + 1) % 4])
                cv2.line(output_frame, pt1, pt2, (0, 255, 0), 4)

        # 绘制中心点 (红色圆点和十字)
        if calibrated_data['center_point'] is not None:
            cx, cy = calibrated_data['center_point']

            # 绘制红色中心点
            cv2.circle(output_frame, (cx, cy), 6, (0, 0, 255), -1)
            cv2.circle(output_frame, (cx, cy), 8, (0, 0, 255), 2)

            # 绘制黑色十字标记
            cross_len = 15
            cv2.line(output_frame, (cx - cross_len, cy), (cx + cross_len, cy), (0, 0, 0), 3)
            cv2.line(output_frame, (cx, cy - cross_len), (cx, cy + cross_len), (0, 0, 0), 3)

            # 显示中心点坐标
            coord_text = f"Center: ({cx}, {cy})"
            cv2.putText(output_frame, coord_text, (cx + 20, cy - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(output_frame, coord_text, (cx + 20, cy - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

        # 显示标定完成状态
        status_text = "CALIBRATED - Square & Center Locked"
        cv2.putText(output_frame, status_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    else:
        # 显示等待标定状态
        status_text = "Press Button to Calibrate Square"
        cv2.putText(output_frame, status_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

    # 显示操作提示
    help_text = "Press 'q' to quit"
    cv2.putText(output_frame, help_text, (10, output_frame.shape[0] - 20),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    return output_frame
