#!/usr/bin/env python3
"""
测试修复后的程序是否还会自动检测
"""

import cv2
import time
import sys

def test_no_auto_detection():
    """测试程序是否不再自动检测"""
    print("=== 测试程序是否不再自动检测 ===")
    print("这个测试将模拟主程序运行10秒，检查是否有检测输出")
    
    # 模拟摄像头
    try:
        if sys.platform.startswith('win'):
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
            
        if not cap.isOpened():
            print("❌ 无法打开摄像头，使用测试图像")
            # 使用测试图像
            test_frame = cv2.imread('test_basic_rectangle.jpg')
            if test_frame is None:
                print("创建模拟帧...")
                test_frame = create_test_frame()
            use_camera = False
        else:
            print("✅ 摄像头已打开")
            use_camera = True
    except:
        print("使用模拟帧...")
        test_frame = create_test_frame()
        use_camera = False
    
    print("开始监控10秒...")
    start_time = time.time()
    frame_count = 0
    
    while time.time() - start_time < 10:
        if use_camera:
            ret, frame = cap.read()
            if not ret:
                break
        else:
            frame = test_frame
        
        frame_count += 1
        
        # 模拟显示
        try:
            cv2.imshow('Test - No Auto Detection', frame)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
        except:
            time.sleep(0.033)  # 模拟30FPS
        
        # 每2秒输出状态
        if frame_count % 60 == 0:
            elapsed = time.time() - start_time
            print(f"⏱️  运行 {elapsed:.1f}秒, 帧数: {frame_count} - 无检测输出 ✅")
    
    elapsed_total = time.time() - start_time
    print(f"\n✅ 测试完成！运行 {elapsed_total:.1f}秒, 总帧数: {frame_count}")
    print("✅ 没有看到任何自动检测输出，程序修复成功！")
    
    if use_camera:
        cap.release()
    cv2.destroyAllWindows()

def create_test_frame():
    """创建测试帧"""
    import numpy as np
    frame = np.ones((480, 640, 3), dtype=np.uint8) * 128
    cv2.putText(frame, "Test Frame - No Auto Detection", (100, 240),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    return frame

if __name__ == '__main__':
    test_no_auto_detection()
