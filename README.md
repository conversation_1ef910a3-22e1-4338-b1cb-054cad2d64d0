# 运动目标控制与自动追踪系统

## 项目简介
本项目是2023年全国大学生电子设计竞赛E题的实现方案，基于OrangePi 5B开发板，使用OpenCV实现正方形和中心点的自动检测与追踪。

### 主要特性
- 基于OrangePi 5B开发板，使用Linux系统
- 支持USB摄像头实时图像采集
- 使用WiringPi库进行GPIO控制
- 基于OpenCV的正方形检测算法
- 支持浅黑色正方形和黑色中心点识别
- GPIO按键控制的一次性标定功能
- 充分注释，便于理解和修改

## 硬件要求
- OrangePi 5B开发板
- USB摄像头
- 按键开关（用于标定触发）

## 引脚连接说明
### GPIO按键连接 (GPIO2_D4)
- **物理引脚**: 22
- **wPi编号**: 13
- **连接方式**: 按键一端接物理引脚22，另一端接GND

## 使用方法
```bash
# 基本运行
sudo python3 main.py

# 无头模式运行（SSH）
sudo python3 main_headless.py

# 测试功能
sudo python3 test_gpio_button.py
python3 test_detection.py
```

## 操作流程
1. 以root权限运行主程序
2. 将包含浅黑色正方形和黑色中心点的白板放在摄像头前
3. 按下GPIO按键进行一次性标定
4. 标定成功后，系统将持续显示标定的正方形边界和中心点

## 文件结构
- `main.py`：主程序入口，带GUI显示版本
- `main_headless.py`：无头模式主程序
- `square_detector.py`：正方形检测器模块
- `utils.py`：工具函数
- `test_*.py`：测试脚本

## 技术特点
- 基于OpenCV轮廓检测算法
- HSV颜色空间识别
- WiringPi GPIO控制
- 实时图像处理
