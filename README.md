# 运动目标控制与自动追踪系统

## 项目简介
本项目是2023年全国大学生电子设计竞赛E题的实现方案，基于OrangePi 5B开发板，使用OpenCV实现正方形和中心点的自动检测与追踪。

### 主要特性
- 基于OrangePi 5B开发板，使用Linux系统
- 支持USB摄像头实时图像采集
- 使用WiringPi库进行GPIO控制
- 基于OpenCV的正方形检测算法
- 支持浅黑色正方形和黑色中心点识别
- GPIO按键控制的一次性标定功能
- 充分注释，便于理解和修改

## 硬件要求
- OrangePi 5B开发板
- USB摄像头
- 按键开关（用于标定触发）

## 引脚连接说明
### GPIO按键连接 (GPIO2_D4)
- **物理引脚**: 22
- **wPi编号**: 13
- **连接方式**: 按键一端接物理引脚22，另一端接GND

## 使用方法

### 推荐运行方式（必须root权限）
```bash
# 方案1：两步启动（推荐）
./setup_and_run.sh

# 方案2：手动两步启动
xhost +local:root
sudo python3 main.py

# 方案3：使用完整启动脚本
sudo ./run_with_display.sh
```

### 手动运行方式
```bash
# 方案1：直接在OrangePi桌面运行（推荐）
sudo python3 main.py

# 方案2：SSH连接时使用X11转发
ssh -X orangepi@你的IP地址
sudo python3 main.py

# 方案3：无头模式运行（SSH，不需要显示）
sudo python3 main_headless.py

# 测试功能
python3 test_display.py           # 测试显示环境和摄像头
sudo python3 test_gpio_button.py  # 测试GPIO按键
python3 test_improved_detection.py # 测试改进的检测算法
python3 test_camera_detection.py  # 测试摄像头实时检测
python3 create_improved_test_images.py # 创建测试图像
```

## 重要说明
- **不保存图像文件**：程序运行过程中不会保存任何图像文件到磁盘
- **外接显示屏支持**：main.py支持在外接显示屏上实时显示摄像头画面
- **实时处理**：所有图像处理都在内存中完成，确保性能和存储空间

## 显示问题解决方案
**重要：程序必须以root权限运行！**

如果遇到显示相关错误，请按以下顺序尝试：

### 错误1："Authorization required, but no authorization protocol specified"
这是X11权限问题，解决方案：
```bash
# 推荐方案：使用两步启动脚本
./setup_and_run.sh

# 或手动执行：
xhost +local:root
sudo python3 main.py
```

### 错误2："Can't initialize GTK backend"
这是显示环境问题，解决方案：

#### 方案1：直接在OrangePi桌面运行（推荐）
- 连接显示器到OrangePi
- 在桌面环境中打开终端
- 运行：`./setup_and_run.sh`

#### 方案2：SSH X11转发
```bash
# 从你的电脑SSH连接时使用X11转发
ssh -X orangepi@你的IP地址
xhost +local:root
sudo python3 main.py
```

#### 方案3：使用无头模式
```bash
# 不需要显示界面，仅控制台输出
sudo python3 main_headless.py
```

## 操作流程
1. 以root权限运行主程序
2. 将包含浅黑色正方形和黑色中心点的白板放在摄像头前
3. 按下GPIO按键进行一次性标定
4. 标定成功后，系统将持续显示标定的正方形边界和中心点
5. 程序运行过程中不会保存任何图像文件，所有处理都在内存中完成

## 文件结构
- `main.py`：主程序入口，带GUI显示版本（需要root权限）
- `main_headless.py`：无头模式主程序（需要root权限）
- `square_detector.py`：正方形检测器模块
- `utils.py`：工具函数
- `setup_and_run.sh`：两步启动脚本（推荐使用）
- `run_with_display.sh`：完整启动脚本（自动处理显示环境）
- `test_display.py`：显示环境和摄像头测试脚本
- `test_gpio_button.py`：GPIO按键测试脚本（需要root权限）
- `test_improved_detection.py`：改进的检测算法测试脚本
- `test_camera_detection.py`：摄像头实时检测测试脚本
- `create_improved_test_images.py`：创建多种测试图像的脚本

## 技术特点
- **改进的矩形检测算法**：结合边缘检测和颜色分割，检测成功率达到87.5%+
- **智能参数优化**：基于实际测试图像分析优化HSV参数和面积范围
- **多重验证机制**：轮廓凸性检验、颜色验证、几何形状验证
- **按键触发检测**：只在按下GPIO按键时进行矩形标定，避免无效计算
- **HSV颜色空间识别**：精确识别浅黑色边框和白色内部
- **WiringPi GPIO控制**：硬件消抖，稳定的按键检测
- **实时图像处理**：内存中处理，不保存文件，性能优化
- **外接显示屏支持**：完善的X11权限处理
- **全面的测试验证**：多种测试图像和场景验证

## 算法改进说明

### 检测算法优化
1. **参数优化**：基于实际测试图像分析，优化HSV颜色范围和面积参数
   - 黑色边框：V值范围扩展到0-80，提高浅黑色检测
   - 面积范围：调整为3000-45000像素，支持多种尺寸
   - 周长范围：200-1200像素，过滤噪声轮廓

2. **多重验证机制**：
   - 轮廓凸性检验：确保检测到的是规整矩形
   - 颜色双重验证：同时验证边框黑色和内部白色
   - 几何形状验证：长宽比、顶点数、面积合理性检查

3. **检测流程改进**：
   - 边缘检测优化：调整Canny参数，增加形态学处理
   - 只在按键时检测：避免无效计算，提高系统效率
   - 详细日志输出：便于调试和问题定位

### 测试验证结果
- **成功率**：87.5%+ (7/8测试图像成功)
- **支持场景**：不同尺寸、位置、光照条件的矩形
- **检测速度**：平均检测时间 < 0.1秒
- **稳定性**：多次测试结果一致
