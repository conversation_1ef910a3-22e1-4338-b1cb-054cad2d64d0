#!/usr/bin/env python3
"""
测试坐标赋值功能
"""

import cv2
from red_laser_detector import RedLaserDetector, get_laser_coordinates, get_center_coordinates, print_all_coordinates

def main():
    print('=== 坐标赋值功能测试 ===')
    
    # 1. 测试中心点坐标赋值
    print('\n1. 测试中心点坐标赋值')
    detector = RedLaserDetector()
    
    # 设置参考数据
    test_center = (700, 400)
    detector.set_reference_data((400, 200, 600, 400), test_center)
    
    # 验证赋值
    x0, y0 = get_center_coordinates()
    print(f'设置的中心点: {test_center}')
    print(f'获取的中心点: x0={x0}, y0={y0}')
    
    if x0 == test_center[0] and y0 == test_center[1]:
        print('✅ 中心点坐标赋值成功')
    else:
        print('❌ 中心点坐标赋值失败')
    
    # 2. 测试激光点坐标赋值
    print('\n2. 测试激光点坐标赋值')
    
    # 检查初始值
    X_init, Y_init = get_laser_coordinates()
    print(f'初始激光点坐标: X={X_init}, Y={Y_init}')
    
    # 加载图像并检测
    frame = cv2.imread('try1.jpg')
    if frame is not None:
        print('图像加载成功，开始检测激光点...')
        laser_point = detector.detect_red_laser(frame)
        
        # 检查赋值结果
        X_after, Y_after = get_laser_coordinates()
        print(f'检测后激光点坐标: X={X_after}, Y={Y_after}')
        print(f'检测函数返回值: {laser_point}')
        
        if laser_point and X_after == laser_point[0] and Y_after == laser_point[1]:
            print('✅ 激光点坐标赋值成功')
        elif laser_point:
            print('❌ 激光点坐标赋值失败')
            print(f'期望: {laser_point}')
            print(f'实际: ({X_after}, {Y_after})')
        else:
            print('⚠️  未检测到激光点')
    else:
        print('❌ 无法加载try1.jpg')
    
    # 3. 打印最终状态
    print('\n3. 最终坐标状态')
    print_all_coordinates()
    
    print('\n🎉 测试完成')

if __name__ == '__main__':
    main()
