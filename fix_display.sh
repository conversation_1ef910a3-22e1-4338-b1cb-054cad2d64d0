#!/bin/bash

# X11显示权限修复脚本
# 解决 "Authorization required, but no authorization protocol specified" 错误

echo "=== X11显示权限修复脚本 ==="
echo ""

# 获取当前用户
CURRENT_USER=$(whoami)
echo "当前用户: $CURRENT_USER"

# 检查是否以root运行
if [ "$CURRENT_USER" = "root" ]; then
    echo "检测到以root用户运行，需要修复X11权限..."
    
    # 获取原始用户（通过SUDO_USER环境变量）
    ORIGINAL_USER=${SUDO_USER:-orangepi}
    echo "原始用户: $ORIGINAL_USER"
    
    # 设置DISPLAY环境变量
    export DISPLAY=:0
    echo "设置 DISPLAY=:0"
    
    # 复制X11认证文件
    if [ -f "/home/<USER>/.Xauthority" ]; then
        echo "复制X11认证文件..."
        cp "/home/<USER>/.Xauthority" "/root/.Xauthority"
        chown root:root "/root/.Xauthority"
        echo "X11认证文件已复制"
    else
        echo "警告: 未找到X11认证文件 /home/<USER>/.Xauthority"
    fi
    
    # 设置xhost权限
    echo "配置xhost权限..."
    su - $ORIGINAL_USER -c "DISPLAY=:0 xhost +local:root" 2>/dev/null || {
        echo "警告: 无法设置xhost权限，尝试其他方法..."
        xhost +local: 2>/dev/null || echo "xhost命令失败"
    }
    
else
    echo "当前不是root用户，设置xhost权限..."
    export DISPLAY=:0
    xhost +local:root 2>/dev/null || echo "警告: xhost命令失败"
fi

echo ""
echo "=== 权限修复完成 ==="
echo "现在可以尝试运行程序:"
echo "sudo python3 main.py"
