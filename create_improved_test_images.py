#!/usr/bin/env python3
"""
创建改进的测试图片
生成多种场景的浅黑色正方形和黑色中心点测试图像
用于验证和改进矩形检测算法
"""

import cv2
import numpy as np
import os

def create_basic_test_image():
    """创建基础测试图像：纯白背景，黑色边框，白色内部，黑色中心点"""
    print("创建基础测试图像...")
    
    # 创建640x480白色背景
    img = np.ones((480, 640, 3), dtype=np.uint8) * 255
    
    # 矩形参数
    center_x, center_y = 320, 240
    rect_width, rect_height = 160, 120
    frame_thickness = 6
    
    # 计算矩形坐标
    x1 = center_x - rect_width // 2
    y1 = center_y - rect_height // 2
    x2 = center_x + rect_width // 2
    y2 = center_y + rect_height // 2
    
    # 绘制黑色边框
    cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 0), frame_thickness)
    
    # 绘制黑色中心点
    cv2.circle(img, (center_x, center_y), 5, (0, 0, 0), -1)
    
    # 保存图像
    filename = 'test_basic_rectangle.jpg'
    cv2.imwrite(filename, img)
    print(f"✓ 保存: {filename}")
    return img, filename

def create_realistic_test_image():
    """创建真实场景测试图像：模拟实际白板拍摄条件"""
    print("创建真实场景测试图像...")
    
    # 创建浅灰色背景（模拟白板）
    img = np.ones((480, 640, 3), dtype=np.uint8) * 240
    
    # 添加轻微噪声
    noise = np.random.normal(0, 5, img.shape).astype(np.int16)
    img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # 矩形参数
    center_x, center_y = 320, 240
    rect_width, rect_height = 180, 140
    frame_thickness = 5
    
    # 计算矩形坐标
    x1 = center_x - rect_width // 2
    y1 = center_y - rect_height // 2
    x2 = center_x + rect_width // 2
    y2 = center_y + rect_height // 2
    
    # 绘制浅黑色边框（深灰色）
    cv2.rectangle(img, (x1, y1), (x2, y2), (50, 50, 50), frame_thickness)
    
    # 矩形内部填充白色
    cv2.rectangle(img, (x1+frame_thickness, y1+frame_thickness), 
                 (x2-frame_thickness, y2-frame_thickness), (250, 250, 250), -1)
    
    # 绘制黑色中心点
    cv2.circle(img, (center_x, center_y), 4, (30, 30, 30), -1)
    
    # 保存图像
    filename = 'test_realistic_rectangle.jpg'
    cv2.imwrite(filename, img)
    print(f"✓ 保存: {filename}")
    return img, filename

def create_size_variations():
    """创建不同尺寸的测试图像"""
    print("创建尺寸变化测试图像...")
    
    sizes = [
        (100, 80, "small"),    # 小尺寸
        (160, 120, "medium"),  # 中等尺寸
        (220, 160, "large")    # 大尺寸
    ]
    
    filenames = []
    for width, height, size_name in sizes:
        # 创建白色背景
        img = np.ones((480, 640, 3), dtype=np.uint8) * 245
        
        center_x, center_y = 320, 240
        frame_thickness = 5
        
        # 计算矩形坐标
        x1 = center_x - width // 2
        y1 = center_y - height // 2
        x2 = center_x + width // 2
        y2 = center_y + height // 2
        
        # 绘制黑色边框
        cv2.rectangle(img, (x1, y1), (x2, y2), (40, 40, 40), frame_thickness)
        
        # 白色内部
        cv2.rectangle(img, (x1+frame_thickness, y1+frame_thickness),
                     (x2-frame_thickness, y2-frame_thickness), (255, 255, 255), -1)
        
        # 黑色中心点
        cv2.circle(img, (center_x, center_y), 4, (20, 20, 20), -1)
        
        # 保存图像
        filename = f'test_{size_name}_rectangle.jpg'
        cv2.imwrite(filename, img)
        filenames.append(filename)
        print(f"✓ 保存: {filename} (尺寸: {width}x{height})")
    
    return filenames

def create_position_variations():
    """创建不同位置的测试图像"""
    print("创建位置变化测试图像...")
    
    positions = [
        (200, 180, "top_left"),
        (320, 240, "center"),
        (450, 300, "bottom_right")
    ]
    
    filenames = []
    for cx, cy, pos_name in positions:
        # 创建白色背景
        img = np.ones((480, 640, 3), dtype=np.uint8) * 245
        
        width, height = 140, 100
        frame_thickness = 5
        
        # 计算矩形坐标，确保在图像范围内
        x1 = max(10, cx - width // 2)
        y1 = max(10, cy - height // 2)
        x2 = min(630, cx + width // 2)
        y2 = min(470, cy + height // 2)
        
        # 绘制黑色边框
        cv2.rectangle(img, (x1, y1), (x2, y2), (35, 35, 35), frame_thickness)
        
        # 白色内部
        cv2.rectangle(img, (x1+frame_thickness, y1+frame_thickness),
                     (x2-frame_thickness, y2-frame_thickness), (255, 255, 255), -1)
        
        # 黑色中心点
        cv2.circle(img, (cx, cy), 4, (15, 15, 15), -1)
        
        # 保存图像
        filename = f'test_{pos_name}_rectangle.jpg'
        cv2.imwrite(filename, img)
        filenames.append(filename)
        print(f"✓ 保存: {filename} (位置: {cx}, {cy})")
    
    return filenames

def analyze_test_image(filename):
    """分析测试图像的特征，用于调试检测算法"""
    print(f"\n=== 分析图像: {filename} ===")
    
    img = cv2.imread(filename)
    if img is None:
        print(f"无法加载图像: {filename}")
        return
    
    # 转换到HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 分析图像统计信息
    print(f"图像尺寸: {img.shape}")
    print(f"灰度值范围: {gray.min()} - {gray.max()}")
    print(f"HSV V通道范围: {hsv[:,:,2].min()} - {hsv[:,:,2].max()}")
    
    # 分析黑色区域（边框和中心点）
    black_mask = cv2.inRange(hsv, np.array([0, 0, 0]), np.array([180, 255, 80]))
    black_pixels = np.sum(black_mask > 0)
    print(f"黑色像素数量: {black_pixels}")
    
    # 分析白色区域（内部）
    white_mask = cv2.inRange(hsv, np.array([0, 0, 180]), np.array([180, 50, 255]))
    white_pixels = np.sum(white_mask > 0)
    print(f"白色像素数量: {white_pixels}")
    
    # 边缘检测分析
    edges = cv2.Canny(gray, 50, 150)
    edge_pixels = np.sum(edges > 0)
    print(f"边缘像素数量: {edge_pixels}")
    
    # 轮廓检测分析
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"检测到轮廓数量: {len(contours)}")
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if area > 100:  # 只分析较大的轮廓
            perimeter = cv2.arcLength(contour, True)
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)
            print(f"  轮廓{i}: 面积={area:.0f}, 周长={perimeter:.0f}, 顶点数={len(approx)}")

def main():
    """主函数：创建所有测试图像并分析"""
    print("=== 创建改进的测试图像 ===")
    
    # 创建测试图像
    basic_img, basic_file = create_basic_test_image()
    realistic_img, realistic_file = create_realistic_test_image()
    size_files = create_size_variations()
    pos_files = create_position_variations()
    
    # 收集所有文件
    all_files = [basic_file, realistic_file] + size_files + pos_files
    
    print(f"\n=== 创建完成，共生成 {len(all_files)} 个测试图像 ===")
    for filename in all_files:
        print(f"  - {filename}")
    
    # 分析测试图像特征
    print("\n=== 分析测试图像特征 ===")
    for filename in all_files[:3]:  # 只分析前3个图像
        analyze_test_image(filename)
    
    print("\n=== 建议的检测参数 ===")
    print("基于分析结果，建议的HSV参数：")
    print("黑色边框: H=[0,180], S=[0,255], V=[0,80]")
    print("白色内部: H=[0,180], S=[0,50], V=[180,255]")
    print("面积范围: 3000-25000像素")
    print("多边形逼近精度: 0.02")

if __name__ == '__main__':
    main()
