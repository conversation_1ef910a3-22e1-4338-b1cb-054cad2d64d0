#!/usr/bin/env python3
"""
红色激光点检测测试脚本
用于验证激光点检测算法的准确性和性能
"""

import cv2
import numpy as np
import time
from red_laser_detector import RedLaserDetector
from utils import draw_laser_detection_results, draw_distance_lines

def create_test_image_with_laser():
    """创建包含红色激光点的测试图像"""
    # 创建白色背景
    img = np.ones((480, 640, 3), dtype=np.uint8) * 255
    
    # 绘制矩形（模拟已标定的矩形）
    rect_x, rect_y, rect_w, rect_h = 200, 150, 240, 180
    cv2.rectangle(img, (rect_x, rect_y), (rect_x + rect_w, rect_y + rect_h), (50, 50, 50), 3)
    
    # 绘制中心点
    center_x, center_y = rect_x + rect_w // 2, rect_y + rect_h // 2
    cv2.circle(img, (center_x, center_y), 5, (0, 0, 0), -1)
    
    # 绘制红色激光点
    laser_x, laser_y = 350, 200
    cv2.circle(img, (laser_x, laser_y), 8, (0, 0, 255), -1)  # 红色激光点
    
    return img, (rect_x, rect_y, rect_w, rect_h), (center_x, center_y), (laser_x, laser_y)

def test_laser_detection_algorithm():
    """测试激光点检测算法"""
    print("=== 红色激光点检测算法测试 ===")
    
    # 创建测试图像
    test_img, square_bounds, center_point, expected_laser = create_test_image_with_laser()
    cv2.imwrite('test_laser_image.jpg', test_img)
    print(f"✅ 测试图像已保存: test_laser_image.jpg")
    print(f"   矩形边界: {square_bounds}")
    print(f"   中心点: {center_point}")
    print(f"   期望激光点: {expected_laser}")
    
    # 初始化检测器
    detector = RedLaserDetector()
    detector.set_reference_data(square_bounds, center_point)
    
    # 进行检测
    print("\\n开始检测...")
    start_time = time.time()
    detected_laser = detector.detect_red_laser(test_img)
    end_time = time.time()
    
    print(f"检测耗时: {end_time - start_time:.3f}秒")
    
    if detected_laser:
        print(f"✅ 检测成功！")
        print(f"   检测到的激光点: {detected_laser}")
        print(f"   期望的激光点: {expected_laser}")
        
        # 计算检测精度
        error_x = abs(detected_laser[0] - expected_laser[0])
        error_y = abs(detected_laser[1] - expected_laser[1])
        total_error = (error_x**2 + error_y**2)**0.5
        
        print(f"   检测误差: X={error_x}px, Y={error_y}px, 总误差={total_error:.1f}px")
        
        # 计算距离
        distances = detector.calculate_distances(detected_laser)
        if distances:
            print(f"\\n📏 距离计算结果:")
            print(f"   到矩形距离 - X: {distances['square_x']}px, Y: {distances['square_y']}px")
            print(f"   到中心距离 - X: {distances['center_x']}px, Y: {distances['center_y']}px")
        
        # 绘制检测结果
        result_img = draw_laser_detection_results(test_img, detector, detected_laser)
        result_img = draw_distance_lines(result_img, detected_laser, square_bounds, center_point)
        cv2.imwrite('test_laser_result.jpg', result_img)
        print(f"✅ 检测结果已保存: test_laser_result.jpg")
        
        return True
    else:
        print("❌ 检测失败，未找到红色激光点")
        return False

def test_laser_detection_performance():
    """测试激光点检测性能"""
    print("\\n=== 性能测试 ===")
    
    # 创建测试图像
    test_img, square_bounds, center_point, _ = create_test_image_with_laser()
    
    # 初始化检测器
    detector = RedLaserDetector()
    detector.set_reference_data(square_bounds, center_point)
    
    # 性能测试
    test_count = 100
    total_time = 0
    success_count = 0
    
    print(f"进行 {test_count} 次检测性能测试...")
    
    for i in range(test_count):
        start_time = time.time()
        result = detector.detect_red_laser(test_img)
        end_time = time.time()
        
        total_time += (end_time - start_time)
        if result:
            success_count += 1
    
    avg_time = total_time / test_count
    success_rate = success_count / test_count * 100
    fps = 1 / avg_time if avg_time > 0 else 0
    
    print(f"\\n📊 性能测试结果:")
    print(f"   平均检测时间: {avg_time:.4f}秒")
    print(f"   成功率: {success_rate:.1f}%")
    print(f"   理论最大帧率: {fps:.1f} FPS")
    
    if fps >= 30:
        print("✅ 性能满足30FPS要求")
    else:
        print("⚠️  性能未达到30FPS要求，需要优化")

def test_hsv_color_range():
    """测试HSV颜色范围设置"""
    print("\\n=== HSV颜色范围测试 ===")
    
    # 创建不同红色的测试点
    test_colors = [
        ((0, 0, 255), "纯红色"),
        ((0, 0, 200), "深红色"),
        ((50, 50, 255), "亮红色"),
        ((0, 100, 255), "饱和红色")
    ]
    
    detector = RedLaserDetector()
    
    for bgr_color, color_name in test_colors:
        # 创建单色测试图像
        test_img = np.ones((100, 100, 3), dtype=np.uint8) * 128  # 灰色背景
        cv2.circle(test_img, (50, 50), 20, bgr_color, -1)  # 彩色圆点
        
        # 转换到HSV查看颜色值
        hsv_img = cv2.cvtColor(test_img, cv2.COLOR_BGR2HSV)
        center_hsv = hsv_img[50, 50]
        
        # 检测测试
        result = detector.detect_red_laser(test_img)
        
        print(f"   {color_name}: BGR{bgr_color} -> HSV{tuple(center_hsv)} -> {'✅检测到' if result else '❌未检测到'}")

def main():
    """主测试函数"""
    print("红色激光点检测器测试工具")
    print("=" * 50)
    
    try:
        # 算法测试
        success = test_laser_detection_algorithm()
        
        if success:
            # 性能测试
            test_laser_detection_performance()
            
            # HSV颜色范围测试
            test_hsv_color_range()
        
        print("\\n" + "=" * 50)
        print("测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
