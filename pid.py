#!/usr/bin/env python3
"""
PID控制系统模块
功能：控制二维云台舵机，使激光点精确移动到指定顶点位置
作者：基于经典PID控制算法实现
硬件：Orange Pi 5B + 360度舵机 + 二维云台
"""

import time
import threading
from PWM import pwm_x, pwm_y, pwm_set
from typing import Tuple, Optional

class PIDController:
    """
    PID控制器类
    用于控制二维云台舵机的精确定位
    """
    
    def __init__(self):
        """初始化PID控制器"""
        
        # ==================== PID参数设置 ====================
        # X轴PID参数（可调节）
        self.kp_x = 1.0      # X轴比例系数
        self.ki_x = 0.1      # X轴积分系数  
        self.kd_x = 0.05     # X轴微分系数
        
        # Y轴PID参数（可调节）
        self.kp_y = 1.0      # Y轴比例系数
        self.ki_y = 0.1      # Y轴积分系数
        self.kd_y = 0.05     # Y轴微分系数
        
        # ==================== PWM参数设置 ====================
        self.pwm_center = 750    # PWM中心值（停止位置）
        self.pwm_min = 250       # PWM最小值
        self.pwm_max = 1250      # PWM最大值
        self.pwm_range = 500     # PWM调节范围（750±500）
        
        # ==================== 控制参数 ====================
        self.max_error_threshold = 5    # 最大误差阈值（像素）
        self.control_frequency = 50     # 控制频率（Hz）
        self.control_period = 1.0 / self.control_frequency
        
        # ==================== 状态变量 ====================
        # X轴PID状态
        self.error_x_prev = 0.0      # X轴上一次误差
        self.integral_x = 0.0        # X轴积分累积
        self.derivative_x = 0.0      # X轴微分
        
        # Y轴PID状态  
        self.error_y_prev = 0.0      # Y轴上一次误差
        self.integral_y = 0.0        # Y轴积分累积
        self.derivative_y = 0.0      # Y轴微分
        
        # 当前PWM输出值
        self.current_pwm_x = self.pwm_center
        self.current_pwm_y = self.pwm_center
        
        # 控制线程相关
        self.control_thread = None
        self.control_running = False
        self.target_position = None  # 目标位置 (x, y)
        self.current_error = None    # 当前误差 (error_x, error_y)
        
        # 线程锁
        self.control_lock = threading.Lock()
        
        print("✅ PID控制器初始化完成")
        print(f"   X轴PID参数: Kp={self.kp_x}, Ki={self.ki_x}, Kd={self.kd_x}")
        print(f"   Y轴PID参数: Kp={self.kp_y}, Ki={self.ki_y}, Kd={self.kd_y}")
        print(f"   PWM范围: {self.pwm_min}-{self.pwm_max}, 中心值: {self.pwm_center}")
    
    def set_pid_parameters(self, kp_x: float = None, ki_x: float = None, kd_x: float = None,
                          kp_y: float = None, ki_y: float = None, kd_y: float = None):
        """
        设置PID参数（方便调节）
        :param kp_x: X轴比例系数
        :param ki_x: X轴积分系数
        :param kd_x: X轴微分系数
        :param kp_y: Y轴比例系数
        :param ki_y: Y轴积分系数
        :param kd_y: Y轴微分系数
        """
        with self.control_lock:
            if kp_x is not None: self.kp_x = kp_x
            if ki_x is not None: self.ki_x = ki_x
            if kd_x is not None: self.kd_x = kd_x
            if kp_y is not None: self.kp_y = kp_y
            if ki_y is not None: self.ki_y = ki_y
            if kd_y is not None: self.kd_y = kd_y
            
        print(f"🔧 PID参数已更新:")
        print(f"   X轴: Kp={self.kp_x}, Ki={self.ki_x}, Kd={self.kd_x}")
        print(f"   Y轴: Kp={self.kp_y}, Ki={self.ki_y}, Kd={self.kd_y}")
    
    def calculate_pid_output(self, error_x: float, error_y: float, dt: float) -> Tuple[float, float]:
        """
        计算PID输出
        :param error_x: X轴误差
        :param error_y: Y轴误差  
        :param dt: 时间间隔
        :return: (pwm_x_output, pwm_y_output)
        """
        # X轴PID计算
        # 比例项
        proportional_x = self.kp_x * error_x
        
        # 积分项（防止积分饱和）
        self.integral_x += error_x * dt
        self.integral_x = max(-100, min(100, self.integral_x))  # 限制积分范围
        integral_x = self.ki_x * self.integral_x
        
        # 微分项
        if dt > 0:
            self.derivative_x = (error_x - self.error_x_prev) / dt
        derivative_x = self.kd_x * self.derivative_x
        
        # X轴总输出
        pid_output_x = proportional_x + integral_x + derivative_x
        
        # Y轴PID计算
        # 比例项
        proportional_y = self.kp_y * error_y
        
        # 积分项（防止积分饱和）
        self.integral_y += error_y * dt
        self.integral_y = max(-100, min(100, self.integral_y))  # 限制积分范围
        integral_y = self.ki_y * self.integral_y
        
        # 微分项
        if dt > 0:
            self.derivative_y = (error_y - self.error_y_prev) / dt
        derivative_y = self.kd_y * self.derivative_y
        
        # Y轴总输出
        pid_output_y = proportional_y + integral_y + derivative_y
        
        # 更新上一次误差
        self.error_x_prev = error_x
        self.error_y_prev = error_y
        
        # 转换为PWM值
        pwm_x_output = self.pwm_center - pid_output_x  # 注意方向
        pwm_y_output = self.pwm_center - pid_output_y  # 注意方向
        
        # 限制PWM范围
        pwm_x_output = max(self.pwm_min, min(self.pwm_max, pwm_x_output))
        pwm_y_output = max(self.pwm_min, min(self.pwm_max, pwm_y_output))
        
        return pwm_x_output, pwm_y_output
    
    def move_to_vertex(self, vertex_index: int, error_x: float, error_y: float):
        """
        移动激光点到指定顶点位置
        :param vertex_index: 顶点索引 (1=左下, 2=左上, 3=右上, 4=右下)
        :param error_x: X方向误差 (xx[vertex_index])
        :param error_y: Y方向误差 (yy[vertex_index])
        """
        print(f"🎯 开始移动到顶点{vertex_index} - 误差: X={error_x:+d}px, Y={error_y:+d}px")
        
        # 设置目标和当前误差
        with self.control_lock:
            self.target_position = (vertex_index, 0, 0)  # 目标是误差为0
            self.current_error = (error_x, error_y)
        
        # 启动控制循环
        if not self.control_running:
            self.start_control_loop()
        
        # 等待到达目标位置
        self._wait_for_target_reached()
    
    def start_control_loop(self):
        """启动PID控制循环"""
        if self.control_running:
            print("⚠️  控制循环已在运行")
            return
        
        self.control_running = True
        self.control_thread = threading.Thread(target=self._control_loop, daemon=True)
        self.control_thread.start()
        print("🚀 PID控制循环已启动")
    
    def stop_control_loop(self):
        """停止PID控制循环"""
        self.control_running = False
        if self.control_thread:
            self.control_thread.join(timeout=1.0)
        
        # 停止舵机
        self.current_pwm_x = self.pwm_center
        self.current_pwm_y = self.pwm_center
        pwm_x(self.current_pwm_x)
        pwm_y(self.current_pwm_y)
        
        print("🛑 PID控制循环已停止，舵机已停止")
    
    def _control_loop(self):
        """PID控制主循环（内部方法）"""
        print("🔄 PID控制循环开始运行...")
        
        last_time = time.time()
        
        while self.control_running:
            try:
                current_time = time.time()
                dt = current_time - last_time
                
                if dt >= self.control_period:
                    with self.control_lock:
                        if self.current_error is not None:
                            error_x, error_y = self.current_error
                            
                            # 计算PID输出
                            pwm_x_output, pwm_y_output = self.calculate_pid_output(error_x, error_y, dt)
                            
                            # 更新当前PWM值
                            self.current_pwm_x = pwm_x_output
                            self.current_pwm_y = pwm_y_output
                            
                            # 输出到舵机
                            pwm_x(int(self.current_pwm_x))
                            pwm_y(int(self.current_pwm_y))
                            
                            # 调试输出（每秒输出一次）
                            if int(current_time) % 1 == 0:
                                print(f"🔄 PID控制: 误差=({error_x:+.1f},{error_y:+.1f}), PWM=({self.current_pwm_x:.0f},{self.current_pwm_y:.0f})")
                    
                    last_time = current_time
                
                time.sleep(0.001)  # 1ms sleep
                
            except Exception as e:
                print(f"❌ PID控制循环异常: {e}")
                break
        
        print("🔄 PID控制循环结束")
    
    def _wait_for_target_reached(self, timeout: float = 10.0):
        """
        等待到达目标位置
        :param timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            with self.control_lock:
                if self.current_error is not None:
                    error_x, error_y = self.current_error
                    
                    # 检查是否到达目标
                    if abs(error_x) <= self.max_error_threshold and abs(error_y) <= self.max_error_threshold:
                        print(f"✅ 已到达目标位置！最终误差: X={error_x:+.1f}px, Y={error_y:+.1f}px")
                        return True
            
            time.sleep(0.1)
        
        print(f"⚠️  到达目标位置超时（{timeout}秒）")
        return False
    
    def reset_pid_state(self):
        """重置PID状态"""
        with self.control_lock:
            self.error_x_prev = 0.0
            self.integral_x = 0.0
            self.derivative_x = 0.0
            self.error_y_prev = 0.0
            self.integral_y = 0.0
            self.derivative_y = 0.0
            
        print("🔄 PID状态已重置")
    
    def get_current_status(self) -> dict:
        """获取当前控制状态"""
        with self.control_lock:
            return {
                'control_running': self.control_running,
                'current_pwm_x': self.current_pwm_x,
                'current_pwm_y': self.current_pwm_y,
                'current_error': self.current_error,
                'target_position': self.target_position,
                'pid_parameters': {
                    'kp_x': self.kp_x, 'ki_x': self.ki_x, 'kd_x': self.kd_x,
                    'kp_y': self.kp_y, 'ki_y': self.ki_y, 'kd_y': self.kd_y
                }
            }

# ==================== 全局PID控制器实例 ====================
# 全局PID控制器实例（单例模式）
_pid_controller = None

def get_pid_controller() -> PIDController:
    """获取PID控制器实例（单例模式）"""
    global _pid_controller
    if _pid_controller is None:
        _pid_controller = PIDController()
        # 初始化PWM
        pwm_set()
    return _pid_controller

# ==================== 便捷控制函数 ====================
def move_laser_to_vertex(vertex_index: int, error_x: float, error_y: float):
    """
    移动激光点到指定顶点
    :param vertex_index: 顶点索引 (1=左下, 2=左上, 3=右上, 4=右下)
    :param error_x: X方向误差 (xx[vertex_index])
    :param error_y: Y方向误差 (yy[vertex_index])
    """
    controller = get_pid_controller()
    controller.move_to_vertex(vertex_index, error_x, error_y)

def set_pid_params(kp_x=None, ki_x=None, kd_x=None, kp_y=None, ki_y=None, kd_y=None):
    """设置PID参数的便捷函数"""
    controller = get_pid_controller()
    controller.set_pid_parameters(kp_x, ki_x, kd_x, kp_y, ki_y, kd_y)

def stop_laser_control():
    """停止激光控制"""
    controller = get_pid_controller()
    controller.stop_control_loop()

def reset_pid():
    """重置PID状态"""
    controller = get_pid_controller()
    controller.reset_pid_state()
