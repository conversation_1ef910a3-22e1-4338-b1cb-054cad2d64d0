import wiringpi
import time

ki = 0.1
kp = 0.1
kd = 0.1
last_error_x = 0
last_error_y = 0



def pid(x,y):
    global X,Y
    a = 0.1
    error_x = X-x
    error_y = Y-y
    error_difference_x = error_x - last_error_x
    error_difference_y = error_y - last_error_y
    integral_x += error_x*a + error_difference_x*(1-a)
    integral_y += error_y*a + error_difference_y*(1-a)
    output_x = kp * error_x + ki * integral_x + kd * error_difference_x
    output_y = kp * error_y + ki * integral_y + kd * error_difference_y
    last_error_x = error_x
    last_error_y = error_y
    return output_x,output_y





