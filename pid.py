import wiringpi
import time
import multiprocessing
import threading
import keyboard
from PWM import pwm_x, pwm_y, pwm_set
from red_laser_detector import get_laser_coordinates, get_center_coordinates

# PID参数
ki = 0.1
kp = 0.1
kd = 0.1

# PID状态变量
last_error_x = 0
last_error_y = 0
integral_x = 0
integral_y = 0

# 全局变量引用
X = 0
Y = 0



def pid(x,y):
    global X, Y, last_error_x, last_error_y, integral_x, integral_y
    a = 0.1
    error_x = X-x
    error_y = Y-y
    error_difference_x = error_x - last_error_x
    error_difference_y = error_y - last_error_y
    integral_x += error_x*a + error_difference_x*(1-a)
    integral_y += error_y*a + error_difference_y*(1-a)
    output_x = kp * error_x + ki * integral_x + kd * error_difference_x
    output_y = kp * error_y + ki * integral_y + kd * error_difference_y
    last_error_x = error_x
    last_error_y = error_y

    pwm_x(output_x)
    pwm_y(output_y)

# ==================== 多进程PID控制系统 ====================

def pid_process_worker(shared_data, control_flag, target_x, target_y):
    """
    PID控制进程工作函数
    :param shared_data: 共享数据字典 (X, Y坐标)
    :param control_flag: 控制标志 (是否运行)
    :param target_x: 目标X坐标
    :param target_y: 目标Y坐标
    """
    global X, Y, last_error_x, last_error_y, integral_x, integral_y

    print(f"🚀 PID控制进程启动 - 目标位置: ({target_x.value}, {target_y.value})")

    # 初始化PWM
    try:
        pwm_set()
        print("✅ PWM初始化成功")
    except Exception as e:
        print(f"❌ PWM初始化失败: {e}")
        return

    # 重置PID状态
    last_error_x = 0
    last_error_y = 0
    integral_x = 0
    integral_y = 0

    # 30Hz控制频率
    control_period = 1.0 / 30.0  # 33.33ms

    while control_flag.value:
        start_time = time.time()

        try:
            # 从共享数据获取当前激光点坐标
            X = shared_data['X']
            Y = shared_data['Y']

            # 调用您的pid函数
            pid(target_x.value, target_y.value)

            # 控制频率为30Hz
            elapsed = time.time() - start_time
            sleep_time = control_period - elapsed
            if sleep_time > 0:
                time.sleep(sleep_time)

        except Exception as e:
            print(f"❌ PID控制进程异常: {e}")
            break

    # 停止时输出750到PWM
    print("🛑 PID控制进程停止，输出750停止舵机")
    try:
        pwm_x(750)
        pwm_y(750)
    except Exception as e:
        print(f"❌ 停止PWM输出失败: {e}")

def start_pid_control_process(target_x, target_y):
    """
    启动PID控制进程
    :param target_x: 目标X坐标
    :param target_y: 目标Y坐标
    :return: (process, shared_data, control_flag, target_x_shared, target_y_shared)
    """
    # 创建共享数据
    manager = multiprocessing.Manager()
    shared_data = manager.dict()
    shared_data['X'] = 0
    shared_data['Y'] = 0

    # 创建控制标志和目标坐标
    control_flag = multiprocessing.Value('b', True)
    target_x_shared = multiprocessing.Value('d', target_x)
    target_y_shared = multiprocessing.Value('d', target_y)

    # 创建并启动进程
    process = multiprocessing.Process(
        target=pid_process_worker,
        args=(shared_data, control_flag, target_x_shared, target_y_shared)
    )
    process.start()

    return process, shared_data, control_flag, target_x_shared, target_y_shared

def update_shared_coordinates(shared_data):
    """
    更新共享坐标数据
    :param shared_data: 共享数据字典
    """
    try:
        # 从red_laser_detector获取最新坐标
        current_X, current_Y = get_laser_coordinates()
        shared_data['X'] = current_X
        shared_data['Y'] = current_Y
    except Exception as e:
        print(f"❌ 更新共享坐标失败: {e}")

def stop_pid_control_process(process, control_flag):
    """
    停止PID控制进程
    :param process: 进程对象
    :param control_flag: 控制标志
    """
    print("🛑 正在停止PID控制进程...")
    control_flag.value = False
    process.join(timeout=2.0)
    if process.is_alive():
        print("⚠️  强制终止PID控制进程")
        process.terminate()
        process.join()
    print("✅ PID控制进程已停止")

