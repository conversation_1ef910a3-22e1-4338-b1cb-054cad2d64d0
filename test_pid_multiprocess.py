#!/usr/bin/env python3
"""
多进程PID控制系统测试脚本
"""

import time
import cv2
from red_laser_detector import RedLaserDetector, get_laser_coordinates, get_center_coordinates
from pid import start_pid_control_process, stop_pid_control_process, update_shared_coordinates

def test_pid_multiprocess():
    """测试多进程PID控制系统"""
    print("=== 多进程PID控制系统测试 ===")
    
    try:
        # 1. 初始化激光点检测器
        print("\n1. 初始化激光点检测器")
        laser_detector = RedLaserDetector()
        
        # 设置测试参数（模拟标定结果）
        test_bounds = (400, 200, 600, 400)
        test_center = (700, 400)
        laser_detector.set_reference_data(test_bounds, test_center)
        
        # 检测激光点（模拟当前位置）
        frame = cv2.imread('try1.jpg')
        if frame is not None:
            laser_point = laser_detector.detect_red_laser(frame)
            print(f"当前激光点位置: {laser_point}")
        else:
            print("⚠️  无法加载测试图像，使用模拟数据")
        
        # 2. 测试PID进程启动
        print("\n2. 测试PID进程启动")
        x0, y0 = get_center_coordinates()
        print(f"目标位置: x0={x0}, y0={y0}")
        
        # 启动PID控制进程
        pid_process, pid_shared_data, pid_control_flag, pid_target_x, pid_target_y = start_pid_control_process(x0, y0)
        print("✅ PID控制进程已启动")
        
        # 3. 模拟运行5秒，定期更新共享数据
        print("\n3. 模拟运行5秒，观察PID控制")
        for i in range(50):  # 5秒，每100ms更新一次
            # 更新共享坐标数据
            update_shared_coordinates(pid_shared_data)
            
            # 每秒输出一次状态
            if i % 10 == 0:
                current_X, current_Y = get_laser_coordinates()
                print(f"第{i//10+1}秒: 当前位置=({current_X}, {current_Y}), 目标位置=({x0}, {y0})")
            
            time.sleep(0.1)
        
        # 4. 停止PID控制进程
        print("\n4. 停止PID控制进程")
        stop_pid_control_process(pid_process, pid_control_flag)
        print("✅ PID控制进程已停止")
        
        print("\n🎉 多进程PID控制系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_keyboard_control_simulation():
    """模拟键盘控制测试"""
    print("\n=== 键盘控制模拟测试 ===")
    
    try:
        # 模拟按'a'键启动PID控制的流程
        print("模拟按'a'键启动PID控制...")
        
        # 1. 检查是否已标定（模拟标定完成状态）
        print("✅ 模拟标定完成状态")
        
        # 2. 获取中心点坐标
        from red_laser_detector import RedLaserDetector
        detector = RedLaserDetector()
        detector.set_reference_data((400, 200, 600, 400), (700, 400))
        x0, y0 = get_center_coordinates()
        print(f"获取目标位置: x0={x0}, y0={y0}")
        
        # 3. 启动PID控制（模拟按'a'键）
        print("启动PID控制进程...")
        pid_process, pid_shared_data, pid_control_flag, _, _ = start_pid_control_process(x0, y0)
        
        # 4. 运行2秒
        print("PID控制运行中...")
        for i in range(20):
            update_shared_coordinates(pid_shared_data)
            time.sleep(0.1)
        
        # 5. 停止PID控制（模拟再次按'a'键）
        print("停止PID控制进程...")
        stop_pid_control_process(pid_process, pid_control_flag)
        
        print("✅ 键盘控制模拟测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 键盘控制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("多进程PID控制系统完整测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行测试
    test_results.append(("多进程PID控制测试", test_pid_multiprocess()))
    test_results.append(("键盘控制模拟测试", test_keyboard_control_simulation()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！多进程PID控制系统准备就绪")
        print("\n📋 使用说明:")
        print("   1. 运行 sudo python3 main.py")
        print("   2. 按'c'或'p'键完成矩形标定")
        print("   3. 按'a'键启动PID控制（激光点移动到中心点）")
        print("   4. 再次按'a'键停止PID控制")
        print("   5. 按'q'键退出程序")
    else:
        print("⚠️  部分测试失败，请检查系统配置")

if __name__ == '__main__':
    main()
