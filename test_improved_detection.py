#!/usr/bin/env python3
"""
测试改进的矩形检测算法
验证各种测试图像的检测效果
"""

import cv2
import numpy as np
import os
from square_detector import SquareDetector
from utils import draw_detection_results

def test_single_image(detector, image_path):
    """测试单个图像的检测效果"""
    print(f"\n{'='*60}")
    print(f"测试图像: {image_path}")
    print('='*60)
    
    # 加载图像
    frame = cv2.imread(image_path)
    if frame is None:
        print(f"❌ 无法加载图像: {image_path}")
        return False
    
    print(f"图像尺寸: {frame.shape}")
    
    # 进行矩形标定
    success = detector.calibrate_square(frame)
    
    if success:
        print("✅ 检测成功！")
        
        # 获取检测结果
        calibrated_data = detector.get_calibrated_data()
        print(f"矩形边界: {calibrated_data['square_bounds']}")
        print(f"中心点坐标: {calibrated_data['center_point']}")
        print(f"矩形顶点数: {len(calibrated_data['square_vertices'])}")
        
        # 绘制检测结果
        result_frame = draw_detection_results(frame, detector, True)
        
        # 保存结果图像
        result_filename = f"result_{os.path.basename(image_path)}"
        cv2.imwrite(result_filename, result_frame)
        print(f"结果已保存: {result_filename}")
        
        # 重置检测器状态，准备下次测试
        detector.reset_calibration()
        
        return True
    else:
        print("❌ 检测失败")
        return False

def test_all_images():
    """测试所有生成的测试图像"""
    print("=== 测试改进的矩形检测算法 ===")
    
    # 初始化检测器
    detector = SquareDetector()
    
    # 测试图像列表
    test_images = [
        'test_basic_rectangle.jpg',
        'test_realistic_rectangle.jpg',
        'test_small_rectangle.jpg',
        'test_medium_rectangle.jpg',
        'test_large_rectangle.jpg',
        'test_top_left_rectangle.jpg',
        'test_center_rectangle.jpg',
        'test_bottom_right_rectangle.jpg'
    ]
    
    # 统计结果
    total_tests = 0
    successful_tests = 0
    
    for image_path in test_images:
        if os.path.exists(image_path):
            total_tests += 1
            if test_single_image(detector, image_path):
                successful_tests += 1
        else:
            print(f"⚠️  测试图像不存在: {image_path}")
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("测试总结")
    print('='*60)
    print(f"总测试数: {total_tests}")
    print(f"成功检测: {successful_tests}")
    print(f"失败检测: {total_tests - successful_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "无测试数据")
    
    if successful_tests == total_tests and total_tests > 0:
        print("🎉 所有测试通过！算法工作正常。")
    elif successful_tests > 0:
        print("⚠️  部分测试通过，算法需要进一步优化。")
    else:
        print("❌ 所有测试失败，算法需要重大改进。")

def test_parameter_tuning():
    """测试不同参数设置的效果"""
    print("\n=== 参数调优测试 ===")
    
    # 使用一个标准测试图像
    test_image = 'test_basic_rectangle.jpg'
    if not os.path.exists(test_image):
        print(f"测试图像不存在: {test_image}")
        return
    
    frame = cv2.imread(test_image)
    
    # 测试不同的HSV参数
    hsv_configs = [
        {
            'name': '默认参数',
            'frame_lower': np.array([0, 0, 0]),
            'frame_upper': np.array([180, 255, 80]),
            'inner_lower': np.array([0, 0, 180]),
            'inner_upper': np.array([180, 50, 255])
        },
        {
            'name': '宽松黑色检测',
            'frame_lower': np.array([0, 0, 0]),
            'frame_upper': np.array([180, 255, 100]),
            'inner_lower': np.array([0, 0, 160]),
            'inner_upper': np.array([180, 60, 255])
        },
        {
            'name': '严格黑色检测',
            'frame_lower': np.array([0, 0, 0]),
            'frame_upper': np.array([180, 255, 60]),
            'inner_lower': np.array([0, 0, 200]),
            'inner_upper': np.array([180, 40, 255])
        }
    ]
    
    for config in hsv_configs:
        print(f"\n--- 测试配置: {config['name']} ---")
        
        detector = SquareDetector()
        detector.set_frame_color_range(config['frame_lower'], config['frame_upper'])
        detector.set_inner_color_range(config['inner_lower'], config['inner_upper'])
        
        success = detector.calibrate_square(frame)
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")

def interactive_test():
    """交互式测试模式"""
    print("\n=== 交互式测试模式 ===")
    print("选择要测试的图像:")
    
    test_images = [
        'test_basic_rectangle.jpg',
        'test_realistic_rectangle.jpg',
        'test_medium_rectangle.jpg'
    ]
    
    for i, img in enumerate(test_images):
        if os.path.exists(img):
            print(f"{i+1}. {img}")
    
    try:
        choice = input("请输入选择 (1-3) 或 'q' 退出: ")
        if choice.lower() == 'q':
            return
        
        idx = int(choice) - 1
        if 0 <= idx < len(test_images) and os.path.exists(test_images[idx]):
            detector = SquareDetector()
            test_single_image(detector, test_images[idx])
            
            # 显示结果
            result_img = cv2.imread(f"result_{test_images[idx]}")
            if result_img is not None:
                cv2.imshow('Detection Result', result_img)
                print("按任意键关闭窗口...")
                cv2.waitKey(0)
                cv2.destroyAllWindows()
        else:
            print("无效选择或文件不存在")
    except:
        print("输入错误")

def main():
    """主函数"""
    print("矩形检测算法测试工具")
    print("请选择测试模式:")
    print("1. 测试所有图像")
    print("2. 参数调优测试")
    print("3. 交互式测试")
    print("4. 退出")
    
    try:
        choice = input("请输入选择 (1-4): ")
        
        if choice == '1':
            test_all_images()
        elif choice == '2':
            test_parameter_tuning()
        elif choice == '3':
            interactive_test()
        elif choice == '4':
            print("退出测试")
        else:
            print("无效选择，直接运行所有测试...")
            test_all_images()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == '__main__':
    main()
