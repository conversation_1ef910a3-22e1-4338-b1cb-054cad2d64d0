# GPIO按键接线指南

## OrangePi 5B GPIO2_D4 按键配置

### 引脚信息
- **GPIO引脚**: GPIO2_D4
- **WiringPi编号**: 13
- **物理引脚**: 22

### 接线方案

#### 方案1：下拉电阻 + 按键接3.3V（推荐）

```
按键配置：
wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)

接线方法：
按键一端 -> 物理引脚22 (GPIO2_D4)
按键另一端 -> 3.3V电源 (物理引脚1或17)

工作原理：
- 未按下：下拉电阻将引脚拉到低电平(0)
- 按下：按键将引脚连接到3.3V，读取高电平(1)

代码检测：
if wiringpi.digitalRead(BUTTON_PIN):  # 高电平 = 按下
    print("按键按下")
```

#### 方案2：上拉电阻 + 按键接GND

```
按键配置：
wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_UP)

接线方法：
按键一端 -> 物理引脚22 (GPIO2_D4)
按键另一端 -> GND (物理引脚6、9、14、20、25、30、34、39)

工作原理：
- 未按下：上拉电阻将引脚拉到高电平(1)
- 按下：按键将引脚连接到GND，读取低电平(0)

代码检测：
if not wiringpi.digitalRead(BUTTON_PIN):  # 低电平 = 按下
    print("按键按下")
```

### OrangePi 5B 引脚图参考

```
物理引脚布局 (40针GPIO):
 1  [3.3V]     [5V]      2
 3  [GPIO2_A1] [5V]      4
 5  [GPIO2_A0] [GND]     6
 7  [GPIO2_A3] [GPIO2_B5] 8
 9  [GND]      [GPIO2_B4] 10
11  [GPIO2_B1] [GPIO2_B6] 12
13  [GPIO2_B2] [GND]     14
15  [GPIO2_B3] [GPIO2_C1] 16
17  [3.3V]     [GPIO2_C0] 18
19  [GPIO2_C3] [GND]     20
21  [GPIO2_C2] [GPIO2_D4] 22  <- 我们使用的引脚
23  [GPIO2_C4] [GPIO2_C5] 24
25  [GND]      [GPIO2_C6] 26
27  [GPIO2_A5] [GPIO2_A4] 28
29  [GPIO2_A7] [GND]     30
31  [GPIO2_A6] [GPIO2_B0] 32
33  [GPIO2_B7] [GND]     34
35  [GPIO2_C7] [GPIO2_D0] 36
37  [GPIO2_D1] [GPIO2_D2] 38
39  [GND]      [GPIO2_D3] 40
```

### 推荐接线（方案1）

```
材料：
- 按键开关 x1
- 杜邦线 x2

连接：
1. 一根杜邦线连接按键一端到物理引脚22 (GPIO2_D4)
2. 另一根杜邦线连接按键另一端到物理引脚1 (3.3V)

优点：
- 按下时为高电平，逻辑直观
- 不容易受到干扰
- 功耗较低
```

### 测试方法

1. **测试GPIO按键**：
```bash
sudo python3 test_gpio_button.py
```

2. **测试主程序**：
```bash
sudo python3 main.py
# 然后按'c'或'p'键进行标定测试
```

### 故障排除

#### 问题1：按键无响应
- 检查接线是否正确
- 确认使用root权限运行
- 检查按键是否损坏

#### 问题2：按键一直触发
- 检查是否接线错误（如下拉电阻配置但接了GND）
- 检查按键是否卡住
- 检查代码中的触发逻辑

#### 问题3：读取值不稳定
- 添加硬件消抖电路
- 增加软件消抖延时
- 检查电源稳定性

### 当前程序配置

目前主程序使用的是**下拉电阻配置**：
```python
wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)
```

请按照**方案1**进行接线：
- 按键一端接物理引脚22
- 按键另一端接物理引脚1 (3.3V)
