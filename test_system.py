#!/usr/bin/env python3
"""
系统测试脚本
测试摄像头、GPIO按键和正方形检测功能
"""

import cv2
import wiringpi
import time
import sys

def test_camera():
    """测试摄像头功能"""
    print("=== 测试摄像头 ===")
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
        if not cap.isOpened():
            print("❌ 摄像头打开失败")
            return False
        
        ret, frame = cap.read()
        if not ret:
            print("❌ 摄像头读取失败")
            cap.release()
            return False
            
        print(f"✅ 摄像头正常，分辨率: {frame.shape[1]}x{frame.shape[0]}")
        cap.release()
        return True
    except Exception as e:
        print(f"❌ 摄像头测试异常: {e}")
        return False

def test_gpio():
    """测试GPIO功能"""
    print("=== 测试GPIO ===")
    try:
        # 初始化WiringPi
        wiringpi.wiringPiSetup()
        
        # 配置按键引脚
        BUTTON_PIN = 13  # GPIO2_D4对应的wPi引脚号
        wiringpi.pinMode(BUTTON_PIN, wiringpi.INPUT)
        wiringpi.pullUpDnControl(BUTTON_PIN, wiringpi.PUD_DOWN)
        
        print(f"✅ GPIO初始化成功")
        print(f"✅ 按键引脚配置完成: wPi {BUTTON_PIN} (物理引脚22)")
        
        # 读取按键状态
        button_state = wiringpi.digitalRead(BUTTON_PIN)
        print(f"✅ 当前按键状态: {button_state}")
        
        return True
    except Exception as e:
        print(f"❌ GPIO测试异常: {e}")
        return False

def test_square_detector():
    """测试正方形检测器"""
    print("=== 测试正方形检测器 ===")
    try:
        from square_detector import SquareDetector
        detector = SquareDetector()
        print("✅ 正方形检测器初始化成功")
        print(f"✅ 正方形颜色范围: {detector.square_hsv_lower} - {detector.square_hsv_upper}")
        print(f"✅ 中心点颜色范围: {detector.center_hsv_lower} - {detector.center_hsv_upper}")
        return True
    except Exception as e:
        print(f"❌ 正方形检测器测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始系统功能测试...")
    print("=" * 50)
    
    # 检查是否以root权限运行
    if wiringpi.wiringPiSetup() == -1:
        print("❌ 需要root权限运行此程序")
        print("请使用: sudo python3 test_system.py")
        return
    
    # 测试各个组件
    camera_ok = test_camera()
    gpio_ok = test_gpio()
    detector_ok = test_square_detector()
    
    print("=" * 50)
    print("测试结果汇总:")
    print(f"摄像头: {'✅ 正常' if camera_ok else '❌ 异常'}")
    print(f"GPIO: {'✅ 正常' if gpio_ok else '❌ 异常'}")
    print(f"正方形检测器: {'✅ 正常' if detector_ok else '❌ 异常'}")
    
    if all([camera_ok, gpio_ok, detector_ok]):
        print("\n🎉 所有组件测试通过！可以运行主程序")
        print("运行命令: sudo python3 main.py")
    else:
        print("\n⚠️  部分组件测试失败，请检查相关配置")

if __name__ == "__main__":
    main()
