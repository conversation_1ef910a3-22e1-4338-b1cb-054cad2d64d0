#!/usr/bin/env python3
"""
红色激光点检测器模块
功能：实时检测红色激光点，计算与矩形和中心点的距离
作者：基于开源OpenCV激光点检测算法改进
"""

import cv2
import numpy as np
import threading
import time
from typing import Tuple, Optional, Dict

class RedLaserDetector:
    """
    红色激光点检测器类
    基于HSV颜色空间进行红色激光点实时检测
    """
    
    def __init__(self):
        """初始化红色激光点检测器"""
        # HSV颜色范围参数 - 针对红色激光点优化（放宽范围）
        # 红色在HSV中有两个范围：0-10和170-180
        self.red_hsv_lower1 = np.array([0, 50, 50])      # 红色范围1下限（降低S和V要求）
        self.red_hsv_upper1 = np.array([15, 255, 255])   # 红色范围1上限（扩大H范围）
        self.red_hsv_lower2 = np.array([165, 50, 50])    # 红色范围2下限（扩大H范围）
        self.red_hsv_upper2 = np.array([180, 255, 255])  # 红色范围2上限
        
        # 激光点检测参数
        self.min_laser_area = 3        # 最小激光点面积（降低要求）
        self.max_laser_area = 2000     # 最大激光点面积（提高上限）
        self.min_circularity = 0.2     # 最小圆形度（降低要求）
        
        # 线程控制
        self.detection_enabled = False  # 检测使能标志
        self.detection_thread = None    # 检测线程
        self.thread_running = False     # 线程运行标志
        self.frame_lock = threading.Lock()  # 帧数据锁
        
        # 检测结果
        self.laser_center = None        # 激光点中心坐标
        self.result_lock = threading.Lock()  # 结果数据锁
        
        # 距离计算结果
        self.distance_to_square_x = 0   # 到矩形X方向距离
        self.distance_to_square_y = 0   # 到矩形Y方向距离  
        self.distance_to_center_x = 0   # 到中心点X方向距离
        self.distance_to_center_y = 0   # 到中心点Y方向距离
        
        # 矩形和中心点信息
        self.square_bounds = None       # 矩形边界 (x, y, w, h)
        self.center_point = None        # 中心点坐标 (x, y)
        
        print("✅ 红色激光点检测器初始化完成")
        print(f"   红色HSV范围1: {self.red_hsv_lower1} - {self.red_hsv_upper1}")
        print(f"   红色HSV范围2: {self.red_hsv_lower2} - {self.red_hsv_upper2}")
        print(f"   激光点面积范围: {self.min_laser_area} - {self.max_laser_area}")
    
    def set_reference_data(self, square_bounds: Tuple[int, int, int, int], 
                          center_point: Tuple[int, int]):
        """
        设置参考数据（矩形边界和中心点）
        :param square_bounds: 矩形边界 (x, y, w, h)
        :param center_point: 中心点坐标 (x, y)
        """
        self.square_bounds = square_bounds
        self.center_point = center_point
        print(f"🎯 设置参考数据 - 矩形: {square_bounds}, 中心点: {center_point}")
    
    def detect_red_laser(self, frame: np.ndarray) -> Optional[Tuple[int, int]]:
        """
        检测红色激光点
        :param frame: 输入图像帧
        :return: 激光点中心坐标 (x, y) 或 None
        """
        if frame is None or frame.size == 0:
            return None
        
        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 创建红色掩码（两个红色范围）
        mask1 = cv2.inRange(hsv, self.red_hsv_lower1, self.red_hsv_upper1)
        mask2 = cv2.inRange(hsv, self.red_hsv_lower2, self.red_hsv_upper2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        # 形态学操作去除噪声
        kernel = np.ones((3, 3), np.uint8)
        red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel)
        red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # 寻找最佳激光点候选
        best_laser = None
        best_score = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # 面积筛选
            if not (self.min_laser_area <= area <= self.max_laser_area):
                continue
            
            # 计算圆形度
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            
            # 圆形度筛选
            if circularity < self.min_circularity:
                continue
            
            # 计算质心
            M = cv2.moments(contour)
            if M["m00"] == 0:
                continue
            
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            
            # 评分：面积 + 圆形度
            score = area * circularity
            if score > best_score:
                best_score = score
                best_laser = (cx, cy)
        
        return best_laser
    
    def calculate_distances(self, laser_point: Tuple[int, int]) -> Dict[str, float]:
        """
        计算激光点到矩形和中心点的距离
        :param laser_point: 激光点坐标 (x, y)
        :return: 距离字典
        """
        if not self.square_bounds or not self.center_point:
            return {}
        
        lx, ly = laser_point
        x, y, w, h = self.square_bounds
        cx, cy = self.center_point
        
        # 计算到矩形边界的最近距离
        # X方向：到左边界或右边界的最近距离
        dist_to_left = abs(lx - x)
        dist_to_right = abs(lx - (x + w))
        distance_to_square_x = min(dist_to_left, dist_to_right)
        
        # Y方向：到上边界或下边界的最近距离
        dist_to_top = abs(ly - y)
        dist_to_bottom = abs(ly - (y + h))
        distance_to_square_y = min(dist_to_top, dist_to_bottom)
        
        # 计算到中心点的距离（分别计算X、Y方向）
        distance_to_center_x = abs(lx - cx)
        distance_to_center_y = abs(ly - cy)
        
        return {
            'square_x': distance_to_square_x,
            'square_y': distance_to_square_y,
            'center_x': distance_to_center_x,
            'center_y': distance_to_center_y
        }
    
    def start_detection(self):
        """启动检测线程"""
        if self.detection_thread and self.thread_running:
            print("⚠️  检测线程已在运行")
            return
        
        self.detection_enabled = True
        self.thread_running = True
        self.detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        self.detection_thread.start()
        print("🚀 红色激光点检测线程已启动")
    
    def stop_detection(self):
        """停止检测线程"""
        self.detection_enabled = False
        self.thread_running = False
        if self.detection_thread:
            self.detection_thread.join(timeout=1.0)
        print("🛑 红色激光点检测线程已停止")
    
    def _detection_loop(self):
        """检测线程主循环（内部方法）"""
        print("🔄 激光点检测循环开始运行...")
        
        while self.thread_running and self.detection_enabled:
            try:
                # 这里需要从主线程获取当前帧
                # 实际实现时会通过共享变量获取
                time.sleep(1/30)  # 30FPS
                
            except Exception as e:
                print(f"❌ 检测线程异常: {e}")
                break
        
        print("🔄 激光点检测循环结束")
    
    def get_detection_results(self) -> Dict:
        """
        获取检测结果
        :return: 检测结果字典
        """
        with self.result_lock:
            return {
                'laser_center': self.laser_center,
                'distance_to_square_x': self.distance_to_square_x,
                'distance_to_square_y': self.distance_to_square_y,
                'distance_to_center_x': self.distance_to_center_x,
                'distance_to_center_y': self.distance_to_center_y
            }
    
    def is_detection_enabled(self) -> bool:
        """检查检测是否启用"""
        return self.detection_enabled
    
    def reset(self):
        """重置检测器状态"""
        self.stop_detection()
        self.laser_center = None
        self.square_bounds = None
        self.center_point = None
        self.distance_to_square_x = 0
        self.distance_to_square_y = 0
        self.distance_to_center_x = 0
        self.distance_to_center_y = 0
        print("🔄 红色激光点检测器已重置")
