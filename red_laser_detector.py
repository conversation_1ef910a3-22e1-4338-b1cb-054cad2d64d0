#!/usr/bin/env python3
"""
红色激光点检测器模块
功能：实时检测红色激光点，计算与矩形和中心点的距离
作者：基于开源OpenCV激光点检测算法改进
"""

import cv2
import numpy as np
import threading
import time
from typing import Tuple, Optional, Dict

# ==================== 全局变量 ====================
# 激光点坐标实时变量
X = 0  # 激光点X坐标
Y = 0  # 激光点Y坐标

# 中心点坐标变量（标定时赋值）
x0 = 0  # 中心点X坐标
y0 = 0  # 中心点Y坐标

class RedLaserDetector:
    """
    红色激光点检测器类
    基于HSV颜色空间进行红色激光点实时检测
    """
    
    def __init__(self):
        """初始化红色激光点检测器"""
        # HSV颜色范围参数 - 针对红色激光点优化
        # 红色在HSV中有两个范围：0-10和170-180
        self.red_hsv_lower1 = np.array([0, 120, 120])    # 红色范围1下限
        self.red_hsv_upper1 = np.array([10, 255, 255])   # 红色范围1上限
        self.red_hsv_lower2 = np.array([170, 120, 120])  # 红色范围2下限
        self.red_hsv_upper2 = np.array([180, 255, 255])  # 红色范围2上限
        
        # 激光点检测参数
        self.min_laser_area = 5        # 最小激光点面积
        self.max_laser_area = 500      # 最大激光点面积
        self.min_circularity = 0.3     # 最小圆形度
        
        # 线程控制
        self.detection_enabled = False  # 检测使能标志
        self.detection_thread = None    # 检测线程
        self.thread_running = False     # 线程运行标志
        self.frame_lock = threading.Lock()  # 帧数据锁
        
        # 检测结果
        self.laser_center = None        # 激光点中心坐标
        self.result_lock = threading.Lock()  # 结果数据锁
        
        # 矩形顶点坐标记录
        self.x1, self.y1 = 0, 0  # 左下顶点
        self.x2, self.y2 = 0, 0  # 左上顶点
        self.x3, self.y3 = 0, 0  # 右上顶点
        self.x4, self.y4 = 0, 0  # 右下顶点

        # 红点到各顶点的坐标差值数组
        self.xx = [0] * 5  # xx[1]到xx[4]分别对应四个顶点，xx[0]不使用
        self.yy = [0] * 5  # yy[1]到yy[4]分别对应四个顶点，yy[0]不使用

        # 矩形和中心点信息
        self.square_bounds = None       # 矩形边界 (x, y, w, h)
        self.center_point = None        # 中心点坐标 (x, y)
        
        print("✅ 红色激光点检测器初始化完成")
        print(f"   红色HSV范围1: {self.red_hsv_lower1} - {self.red_hsv_upper1}")
        print(f"   红色HSV范围2: {self.red_hsv_lower2} - {self.red_hsv_upper2}")
        print(f"   激光点面积范围: {self.min_laser_area} - {self.max_laser_area}")
    
    def set_reference_data(self, square_bounds: Tuple[int, int, int, int],
                          center_point: Tuple[int, int]):
        """
        设置参考数据（矩形边界和中心点），并计算四个顶点坐标
        :param square_bounds: 矩形边界 (x, y, w, h)
        :param center_point: 中心点坐标 (x, y)
        """
        global x0, y0  # 声明全局变量

        self.square_bounds = square_bounds
        self.center_point = center_point

        # 赋值中心点坐标给全局变量x0、y0
        x0, y0 = center_point

        # 计算矩形四个顶点坐标
        x, y, w, h = square_bounds
        self.x1, self.y1 = x, y + h      # 左下顶点
        self.x2, self.y2 = x, y          # 左上顶点
        self.x3, self.y3 = x + w, y      # 右上顶点
        self.x4, self.y4 = x + w, y + h  # 右下顶点

        print(f"🎯 设置参考数据:")
        print(f"   矩形边界: {square_bounds}")
        print(f"   中心点: {center_point}")
        print(f"   ✅ 中心点坐标已赋值: x0={x0}, y0={y0}")
        print(f"   四个顶点坐标:")
        print(f"     左下(x1,y1): ({self.x1}, {self.y1})")
        print(f"     左上(x2,y2): ({self.x2}, {self.y2})")
        print(f"     右上(x3,y3): ({self.x3}, {self.y3})")
        print(f"     右下(x4,y4): ({self.x4}, {self.y4})")
    
    def detect_red_laser(self, frame: np.ndarray) -> Optional[Tuple[int, int]]:
        """
        检测红色激光点 - 改进算法：寻找图像中红色最亮的点
        基于try1.jpg的特征，使用亮度和红色通道结合的方法
        :param frame: 输入图像帧
        :return: 激光点中心坐标 (x, y) 或 None
        """
        if frame is None or frame.size == 0:
            return None

        # 方法1：基于红色通道亮度检测
        red_channel = frame[:, :, 2]  # BGR中的R通道

        # 找到红色通道的最大值
        max_red_value = red_channel.max()
        if max_red_value < 200:  # 如果最大红色值太低，可能没有激光点
            return None

        # 找到红色通道最亮的区域（前5%的像素）
        threshold = max(200, int(max_red_value * 0.95))  # 至少200，或95%的最大值
        bright_red_mask = red_channel >= threshold

        # 结合HSV颜色筛选，确保是红色区域
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

        # 创建红色掩码（放宽条件以适应激光点）
        mask1 = cv2.inRange(hsv, np.array([0, 30, 150]), np.array([15, 255, 255]))
        mask2 = cv2.inRange(hsv, np.array([165, 30, 150]), np.array([180, 255, 255]))
        red_color_mask = cv2.bitwise_or(mask1, mask2)

        # 结合亮度掩码和颜色掩码
        combined_mask = cv2.bitwise_and(bright_red_mask.astype(np.uint8) * 255, red_color_mask)

        # 如果没有找到符合条件的像素，使用纯亮度方法
        if np.sum(combined_mask) == 0:
            combined_mask = bright_red_mask.astype(np.uint8) * 255

        # 形态学操作去除噪声
        kernel = np.ones((3, 3), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)

        # 查找连通区域
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            # 如果没有找到轮廓，直接使用最亮点
            max_locations = np.where(red_channel == max_red_value)
            if len(max_locations[0]) > 0:
                # 计算所有最亮点的质心
                center_y = int(np.mean(max_locations[0]))
                center_x = int(np.mean(max_locations[1]))
                best_laser = (center_x, center_y)
            else:
                best_laser = None
        else:
            # 寻找最亮的连通区域
            best_laser = None
            max_brightness = 0

            for contour in contours:
                # 创建轮廓掩码
                mask = np.zeros(red_channel.shape, dtype=np.uint8)
                cv2.fillPoly(mask, [contour], 255)

                # 计算该区域的平均亮度
                region_pixels = red_channel[mask > 0]
                if len(region_pixels) == 0:
                    continue

                avg_brightness = np.mean(region_pixels)
                area = cv2.contourArea(contour)

                # 综合评分：亮度 + 面积权重
                score = avg_brightness * (1 + area / 1000)  # 面积越大权重稍微增加

                if score > max_brightness:
                    max_brightness = score

                    # 使用灰度质心法计算精确中心
                    # 在轮廓区域内计算加权质心
                    moments = cv2.moments(contour)
                    if moments["m00"] != 0:
                        cx = int(moments["m10"] / moments["m00"])
                        cy = int(moments["m01"] / moments["m00"])

                        # 进一步精确：在小区域内使用亮度加权质心
                        roi_size = 10
                        y1, y2 = max(0, cy - roi_size), min(frame.shape[0], cy + roi_size)
                        x1, x2 = max(0, cx - roi_size), min(frame.shape[1], cx + roi_size)

                        roi_red = red_channel[y1:y2, x1:x2]
                        if roi_red.size > 0:
                            # 亮度加权质心
                            y_coords, x_coords = np.mgrid[0:roi_red.shape[0], 0:roi_red.shape[1]]
                            total_weight = np.sum(roi_red)
                            if total_weight > 0:
                                weighted_cx = int(np.sum(x_coords * roi_red) / total_weight) + x1
                                weighted_cy = int(np.sum(y_coords * roi_red) / total_weight) + y1
                                best_laser = (weighted_cx, weighted_cy)
                            else:
                                best_laser = (cx, cy)
                        else:
                            best_laser = (cx, cy)

        # 实时赋值激光点坐标给全局变量X、Y
        global X, Y
        if best_laser is not None:
            X, Y = best_laser
            print(f"🔴 激光点坐标实时更新: X={X}, Y={Y}")
        else:
            # 如果没有检测到激光点，保持上一次的值或设为0
            if X == 0 and Y == 0:
                print("🔴 未检测到激光点，X、Y保持为0")

        return best_laser
    
    def calculate_distances(self, laser_point: Tuple[int, int]) -> Dict[str, float]:
        """
        计算激光点到矩形四个顶点的坐标差值
        :param laser_point: 激光点坐标 (x, y)
        :return: 距离字典
        """
        if not self.square_bounds or not self.center_point:
            return {}

        lx, ly = laser_point

        # 计算红点到四个顶点的坐标差值（红点坐标 - 顶点坐标）
        self.xx[1] = lx - self.x1  # 红点X - 左下顶点X
        self.yy[1] = ly - self.y1  # 红点Y - 左下顶点Y

        self.xx[2] = lx - self.x2  # 红点X - 左上顶点X
        self.yy[2] = ly - self.y2  # 红点Y - 左上顶点Y

        self.xx[3] = lx - self.x3  # 红点X - 右上顶点X
        self.yy[3] = ly - self.y3  # 红点Y - 右上顶点Y

        self.xx[4] = lx - self.x4  # 红点X - 右下顶点X
        self.yy[4] = ly - self.y4  # 红点Y - 右下顶点Y

        # 终端输出详细信息
        print(f"🔍 红点到四个顶点的坐标差值计算:")
        print(f"   激光点坐标: ({lx}, {ly})")
        print(f"   左下顶点(x1,y1): ({self.x1}, {self.y1}) -> xx[1]={self.xx[1]}, yy[1]={self.yy[1]}")
        print(f"   左上顶点(x2,y2): ({self.x2}, {self.y2}) -> xx[2]={self.xx[2]}, yy[2]={self.yy[2]}")
        print(f"   右上顶点(x3,y3): ({self.x3}, {self.y3}) -> xx[3]={self.xx[3]}, yy[3]={self.yy[3]}")
        print(f"   右下顶点(x4,y4): ({self.x4}, {self.y4}) -> xx[4]={self.xx[4]}, yy[4]={self.yy[4]}")

        return {
            'xx1': self.xx[1], 'yy1': self.yy[1],  # 到左下顶点
            'xx2': self.xx[2], 'yy2': self.yy[2],  # 到左上顶点
            'xx3': self.xx[3], 'yy3': self.yy[3],  # 到右上顶点
            'xx4': self.xx[4], 'yy4': self.yy[4],  # 到右下顶点
            'vertices': {
                'left_bottom': (self.x1, self.y1),
                'left_top': (self.x2, self.y2),
                'right_top': (self.x3, self.y3),
                'right_bottom': (self.x4, self.y4)
            }
        }
    
    def start_detection(self):
        """启动检测线程"""
        if self.detection_thread and self.thread_running:
            print("⚠️  检测线程已在运行")
            return
        
        self.detection_enabled = True
        self.thread_running = True
        self.detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        self.detection_thread.start()
        print("🚀 红色激光点检测线程已启动")
    
    def stop_detection(self):
        """停止检测线程"""
        self.detection_enabled = False
        self.thread_running = False
        if self.detection_thread:
            self.detection_thread.join(timeout=1.0)
        print("🛑 红色激光点检测线程已停止")
    
    def _detection_loop(self):
        """检测线程主循环（内部方法）"""
        print("🔄 激光点检测循环开始运行...")
        
        while self.thread_running and self.detection_enabled:
            try:
                # 这里需要从主线程获取当前帧
                # 实际实现时会通过共享变量获取
                time.sleep(1/30)  # 30FPS
                
            except Exception as e:
                print(f"❌ 检测线程异常: {e}")
                break
        
        print("🔄 激光点检测循环结束")
    
    def get_detection_results(self) -> Dict:
        """
        获取检测结果
        :return: 检测结果字典
        """
        with self.result_lock:
            return {
                'laser_center': self.laser_center,
                'distance_to_square_x': self.distance_to_square_x,
                'distance_to_square_y': self.distance_to_square_y,
                'distance_to_center_x': self.distance_to_center_x,
                'distance_to_center_y': self.distance_to_center_y
            }
    
    def is_detection_enabled(self) -> bool:
        """检查检测是否启用"""
        return self.detection_enabled
    
    def reset(self):
        """重置检测器状态"""
        self.stop_detection()
        self.laser_center = None
        self.square_bounds = None
        self.center_point = None
        self.distance_to_square_x = 0
        self.distance_to_square_y = 0
        self.distance_to_center_x = 0
        self.distance_to_center_y = 0
        print("🔄 红色激光点检测器已重置")

# ==================== 全局变量访问函数 ====================
def get_laser_coordinates():
    """
    获取当前激光点坐标
    :return: (X, Y) 激光点坐标
    """
    global X, Y
    return X, Y

def get_center_coordinates():
    """
    获取中心点坐标
    :return: (x0, y0) 中心点坐标
    """
    global x0, y0
    return x0, y0

def print_all_coordinates():
    """
    打印所有坐标信息
    """
    global X, Y, x0, y0
    print(f"📍 当前坐标状态:")
    print(f"   激光点坐标: X={X}, Y={Y}")
    print(f"   中心点坐标: x0={x0}, y0={y0}")
