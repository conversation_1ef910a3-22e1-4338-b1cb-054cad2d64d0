#!/usr/bin/env python3
"""
红色激光点检测器模块
功能：实时检测红色激光点，计算与矩形和中心点的距离
作者：基于开源OpenCV激光点检测算法改进
"""

import cv2
import numpy as np
import threading
import time
from typing import Tuple, Optional, Dict

class RedLaserDetector:
    """
    红色激光点检测器类
    基于HSV颜色空间进行红色激光点实时检测
    """
    
    def __init__(self):
        """初始化红色激光点检测器"""
        # HSV颜色范围参数 - 针对红色激光点优化
        # 红色在HSV中有两个范围：0-10和170-180
        self.red_hsv_lower1 = np.array([0, 120, 120])    # 红色范围1下限
        self.red_hsv_upper1 = np.array([10, 255, 255])   # 红色范围1上限
        self.red_hsv_lower2 = np.array([170, 120, 120])  # 红色范围2下限
        self.red_hsv_upper2 = np.array([180, 255, 255])  # 红色范围2上限
        
        # 激光点检测参数
        self.min_laser_area = 5        # 最小激光点面积
        self.max_laser_area = 500      # 最大激光点面积
        self.min_circularity = 0.3     # 最小圆形度
        
        # 线程控制
        self.detection_enabled = False  # 检测使能标志
        self.detection_thread = None    # 检测线程
        self.thread_running = False     # 线程运行标志
        self.frame_lock = threading.Lock()  # 帧数据锁
        
        # 检测结果
        self.laser_center = None        # 激光点中心坐标
        self.result_lock = threading.Lock()  # 结果数据锁
        
        # 距离计算结果 - 按要求修改变量名
        self.xx = 0    # 激光点到边框的X方向最短距离
        self.yy = 0    # 激光点到边框的Y方向最短距离
        self.xx1 = 0   # 激光点到中心点的X方向距离
        self.yy1 = 0   # 激光点到中心点的Y方向距离
        
        # 矩形和中心点信息
        self.square_bounds = None       # 矩形边界 (x, y, w, h)
        self.center_point = None        # 中心点坐标 (x, y)
        
        print("✅ 红色激光点检测器初始化完成")
        print(f"   红色HSV范围1: {self.red_hsv_lower1} - {self.red_hsv_upper1}")
        print(f"   红色HSV范围2: {self.red_hsv_lower2} - {self.red_hsv_upper2}")
        print(f"   激光点面积范围: {self.min_laser_area} - {self.max_laser_area}")
    
    def set_reference_data(self, square_bounds: Tuple[int, int, int, int], 
                          center_point: Tuple[int, int]):
        """
        设置参考数据（矩形边界和中心点）
        :param square_bounds: 矩形边界 (x, y, w, h)
        :param center_point: 中心点坐标 (x, y)
        """
        self.square_bounds = square_bounds
        self.center_point = center_point
        print(f"🎯 设置参考数据 - 矩形: {square_bounds}, 中心点: {center_point}")
    
    def detect_red_laser(self, frame: np.ndarray) -> Optional[Tuple[int, int]]:
        """
        检测红色激光点 - 改进算法：寻找图像中红色最亮的点
        基于try1.jpg的特征，使用亮度和红色通道结合的方法
        :param frame: 输入图像帧
        :return: 激光点中心坐标 (x, y) 或 None
        """
        if frame is None or frame.size == 0:
            return None

        # 方法1：基于红色通道亮度检测
        red_channel = frame[:, :, 2]  # BGR中的R通道

        # 找到红色通道的最大值
        max_red_value = red_channel.max()
        if max_red_value < 200:  # 如果最大红色值太低，可能没有激光点
            return None

        # 找到红色通道最亮的区域（前5%的像素）
        threshold = max(200, int(max_red_value * 0.95))  # 至少200，或95%的最大值
        bright_red_mask = red_channel >= threshold

        # 结合HSV颜色筛选，确保是红色区域
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

        # 创建红色掩码（放宽条件以适应激光点）
        mask1 = cv2.inRange(hsv, np.array([0, 30, 150]), np.array([15, 255, 255]))
        mask2 = cv2.inRange(hsv, np.array([165, 30, 150]), np.array([180, 255, 255]))
        red_color_mask = cv2.bitwise_or(mask1, mask2)

        # 结合亮度掩码和颜色掩码
        combined_mask = cv2.bitwise_and(bright_red_mask.astype(np.uint8) * 255, red_color_mask)

        # 如果没有找到符合条件的像素，使用纯亮度方法
        if np.sum(combined_mask) == 0:
            combined_mask = bright_red_mask.astype(np.uint8) * 255

        # 形态学操作去除噪声
        kernel = np.ones((3, 3), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)

        # 查找连通区域
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            # 如果没有找到轮廓，直接使用最亮点
            max_locations = np.where(red_channel == max_red_value)
            if len(max_locations[0]) > 0:
                # 计算所有最亮点的质心
                center_y = int(np.mean(max_locations[0]))
                center_x = int(np.mean(max_locations[1]))
                return (center_x, center_y)
            return None

        # 寻找最亮的连通区域
        best_laser = None
        max_brightness = 0

        for contour in contours:
            # 创建轮廓掩码
            mask = np.zeros(red_channel.shape, dtype=np.uint8)
            cv2.fillPoly(mask, [contour], 255)

            # 计算该区域的平均亮度
            region_pixels = red_channel[mask > 0]
            if len(region_pixels) == 0:
                continue

            avg_brightness = np.mean(region_pixels)
            area = cv2.contourArea(contour)

            # 综合评分：亮度 + 面积权重
            score = avg_brightness * (1 + area / 1000)  # 面积越大权重稍微增加

            if score > max_brightness:
                max_brightness = score

                # 使用灰度质心法计算精确中心
                # 在轮廓区域内计算加权质心
                moments = cv2.moments(contour)
                if moments["m00"] != 0:
                    cx = int(moments["m10"] / moments["m00"])
                    cy = int(moments["m01"] / moments["m00"])

                    # 进一步精确：在小区域内使用亮度加权质心
                    roi_size = 10
                    y1, y2 = max(0, cy - roi_size), min(frame.shape[0], cy + roi_size)
                    x1, x2 = max(0, cx - roi_size), min(frame.shape[1], cx + roi_size)

                    roi_red = red_channel[y1:y2, x1:x2]
                    if roi_red.size > 0:
                        # 亮度加权质心
                        y_coords, x_coords = np.mgrid[0:roi_red.shape[0], 0:roi_red.shape[1]]
                        total_weight = np.sum(roi_red)
                        if total_weight > 0:
                            weighted_cx = int(np.sum(x_coords * roi_red) / total_weight) + x1
                            weighted_cy = int(np.sum(y_coords * roi_red) / total_weight) + y1
                            best_laser = (weighted_cx, weighted_cy)
                        else:
                            best_laser = (cx, cy)
                    else:
                        best_laser = (cx, cy)

        return best_laser
    
    def calculate_distances(self, laser_point: Tuple[int, int]) -> Dict[str, float]:
        """
        计算激光点到矩形边框和中心点的坐标差值（带正负号）
        :param laser_point: 激光点坐标 (x, y)
        :return: 距离字典
        """
        if not self.square_bounds or not self.center_point:
            return {}

        lx, ly = laser_point
        x, y, w, h = self.square_bounds
        cx, cy = self.center_point

        # 1. 找出红点到矩形框上最近的点
        # 计算矩形框上最近的点坐标
        if lx < x:
            # 激光点在矩形左侧，最近点在左边界
            nearest_x = x
        elif lx > x + w:
            # 激光点在矩形右侧，最近点在右边界
            nearest_x = x + w
        else:
            # 激光点在矩形X范围内，最近点X坐标就是激光点X坐标
            nearest_x = lx

        if ly < y:
            # 激光点在矩形上方，最近点在上边界
            nearest_y = y
        elif ly > y + h:
            # 激光点在矩形下方，最近点在下边界
            nearest_y = y + h
        else:
            # 激光点在矩形Y范围内，最近点Y坐标就是激光点Y坐标
            nearest_y = ly

        # 2. 计算坐标差值（红点坐标 - 矩形框最近点坐标）
        distance_to_square_x = lx - nearest_x  # 带正负号
        distance_to_square_y = ly - nearest_y  # 带正负号

        # 3. 计算到中心点的坐标差值（红点坐标 - 中心点坐标）
        distance_to_center_x = lx - cx  # 带正负号
        distance_to_center_y = ly - cy  # 带正负号

        # 更新实例变量
        self.xx = distance_to_square_x   # 到边框X方向坐标差值（带正负号）
        self.yy = distance_to_square_y   # 到边框Y方向坐标差值（带正负号）
        self.xx1 = distance_to_center_x  # 到中心点X方向坐标差值（带正负号）
        self.yy1 = distance_to_center_y  # 到中心点Y方向坐标差值（带正负号）

        # 调试信息
        print(f"🔍 距离计算详情:")
        print(f"   激光点坐标: ({lx}, {ly})")
        print(f"   矩形框: ({x}, {y}, {w}, {h})")
        print(f"   矩形框最近点: ({nearest_x}, {nearest_y})")
        print(f"   中心点: ({cx}, {cy})")
        print(f"   xx = {lx} - {nearest_x} = {distance_to_square_x}")
        print(f"   yy = {ly} - {nearest_y} = {distance_to_square_y}")
        print(f"   xx1 = {lx} - {cx} = {distance_to_center_x}")
        print(f"   yy1 = {ly} - {cy} = {distance_to_center_y}")

        return {
            'square_x': distance_to_square_x,
            'square_y': distance_to_square_y,
            'center_x': distance_to_center_x,
            'center_y': distance_to_center_y,
            'xx': self.xx,
            'yy': self.yy,
            'xx1': self.xx1,
            'yy1': self.yy1,
            'nearest_point': (nearest_x, nearest_y)  # 添加最近点信息
        }
    
    def start_detection(self):
        """启动检测线程"""
        if self.detection_thread and self.thread_running:
            print("⚠️  检测线程已在运行")
            return
        
        self.detection_enabled = True
        self.thread_running = True
        self.detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        self.detection_thread.start()
        print("🚀 红色激光点检测线程已启动")
    
    def stop_detection(self):
        """停止检测线程"""
        self.detection_enabled = False
        self.thread_running = False
        if self.detection_thread:
            self.detection_thread.join(timeout=1.0)
        print("🛑 红色激光点检测线程已停止")
    
    def _detection_loop(self):
        """检测线程主循环（内部方法）"""
        print("🔄 激光点检测循环开始运行...")
        
        while self.thread_running and self.detection_enabled:
            try:
                # 这里需要从主线程获取当前帧
                # 实际实现时会通过共享变量获取
                time.sleep(1/30)  # 30FPS
                
            except Exception as e:
                print(f"❌ 检测线程异常: {e}")
                break
        
        print("🔄 激光点检测循环结束")
    
    def get_detection_results(self) -> Dict:
        """
        获取检测结果
        :return: 检测结果字典
        """
        with self.result_lock:
            return {
                'laser_center': self.laser_center,
                'distance_to_square_x': self.distance_to_square_x,
                'distance_to_square_y': self.distance_to_square_y,
                'distance_to_center_x': self.distance_to_center_x,
                'distance_to_center_y': self.distance_to_center_y
            }
    
    def is_detection_enabled(self) -> bool:
        """检查检测是否启用"""
        return self.detection_enabled
    
    def reset(self):
        """重置检测器状态"""
        self.stop_detection()
        self.laser_center = None
        self.square_bounds = None
        self.center_point = None
        self.distance_to_square_x = 0
        self.distance_to_square_y = 0
        self.distance_to_center_x = 0
        self.distance_to_center_y = 0
        print("🔄 红色激光点检测器已重置")
