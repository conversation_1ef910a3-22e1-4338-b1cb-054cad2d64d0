#!/usr/bin/env python3
"""
验证程序确实只在按键时进行检测
通过监控检测方法的调用来确认
"""

import cv2
import time
import sys
from square_detector import SquareDetector
from utils import draw_detection_results

class MonitoredSquareDetector(SquareDetector):
    """
    带监控功能的正方形检测器
    用于验证检测方法的调用情况
    """
    
    def __init__(self):
        super().__init__()
        self.detection_call_count = 0
        self.last_detection_time = None
        
    def detect_rectangle_contours(self, frame):
        """重写检测方法，添加监控"""
        self.detection_call_count += 1
        self.last_detection_time = time.time()
        
        print(f"🔍 [监控] 检测方法被调用！调用次数: {self.detection_call_count}")
        print(f"🔍 [监控] 调用时间: {time.strftime('%H:%M:%S', time.localtime(self.last_detection_time))}")
        
        # 调用原始检测方法
        return super().detect_rectangle_contours(frame)
    
    def calibrate_square(self, frame):
        """重写标定方法，添加监控"""
        print(f"📍 [监控] 标定方法被调用！")
        return super().calibrate_square(frame)
    
    def get_monitoring_info(self):
        """获取监控信息"""
        return {
            'detection_calls': self.detection_call_count,
            'last_detection': self.last_detection_time
        }

def test_no_continuous_detection():
    """测试程序是否真的不进行持续检测"""
    print("=== 验证程序不进行持续检测 ===")
    print("这个测试将运行30秒，监控检测方法的调用情况")
    print("如果程序正确，应该看到：")
    print("1. 帧数持续增加")
    print("2. 检测方法调用次数为0（除非手动触发）")
    print("3. 只有显示相关的输出")
    print()
    
    # 初始化监控检测器
    detector = MonitoredSquareDetector()
    
    # 模拟摄像头（使用测试图像）
    test_frame = None
    try:
        test_frame = cv2.imread('test_basic_rectangle.jpg')
        if test_frame is None:
            # 如果没有测试图像，创建一个简单的
            test_frame = cv2.imread('test_square.jpg')
            if test_frame is None:
                print("创建模拟帧...")
                test_frame = create_simple_test_frame()
    except:
        print("创建模拟帧...")
        test_frame = create_simple_test_frame()
    
    print("开始监控...")
    start_time = time.time()
    frame_count = 0
    calibration_done = False
    
    # 模拟主程序循环（不包含按键检测）
    while time.time() - start_time < 30:  # 运行30秒
        frame_count += 1
        
        # 模拟主程序的显示逻辑（不包含检测）
        display_frame = draw_detection_results(test_frame, detector, calibration_done)
        
        # 添加监控信息显示
        monitoring_info = detector.get_monitoring_info()
        info_text = f"Frame: {frame_count}, Detections: {monitoring_info['detection_calls']}"
        cv2.putText(display_frame, info_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 每5秒输出一次状态
        if frame_count % 150 == 0:  # 假设30FPS
            elapsed = time.time() - start_time
            print(f"⏱️  运行时间: {elapsed:.1f}秒, 帧数: {frame_count}, 检测调用次数: {monitoring_info['detection_calls']}")
        
        # 模拟显示（在实际环境中会显示到屏幕）
        try:
            cv2.imshow('Monitoring - No Continuous Detection', display_frame)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('t'):  # 按't'键手动触发一次检测
                print("🔥 手动触发检测...")
                detector.calibrate_square(test_frame)
                calibration_done = True
        except:
            # 如果无法显示，继续运行但不显示
            time.sleep(0.033)  # 模拟30FPS
    
    # 输出最终结果
    final_info = detector.get_monitoring_info()
    elapsed_total = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("监控结果:")
    print(f"总运行时间: {elapsed_total:.1f}秒")
    print(f"总帧数: {frame_count}")
    print(f"检测方法调用次数: {final_info['detection_calls']}")
    print(f"平均帧率: {frame_count/elapsed_total:.1f} FPS")
    
    if final_info['detection_calls'] == 0:
        print("✅ 验证通过：程序没有进行持续检测！")
        print("✅ 只有在手动触发时才会进行检测。")
    else:
        print("⚠️  检测到检测方法被调用，请检查代码逻辑。")
        if final_info['last_detection']:
            print(f"最后一次检测时间: {time.strftime('%H:%M:%S', time.localtime(final_info['last_detection']))}")
    
    cv2.destroyAllWindows()

def create_simple_test_frame():
    """创建简单的测试帧"""
    frame = cv2.imread('test_basic_rectangle.jpg')
    if frame is not None:
        return frame
        
    # 如果没有测试图像，创建一个简单的白色帧
    frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
    cv2.putText(frame, "Test Frame", (250, 240),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    return frame

def main():
    """主函数"""
    print("程序持续检测验证工具")
    print("用于验证主程序是否真的只在按键时进行检测")
    print()
    
    try:
        test_no_continuous_detection()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    import numpy as np
    main()
