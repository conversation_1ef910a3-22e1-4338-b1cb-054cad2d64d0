#!/usr/bin/env python3
"""
测试正方形检测功能
使用生成的测试图像验证检测算法
"""

import cv2
import numpy as np
from square_detector import SquareDetector

def test_square_detection():
    """
    测试正方形检测功能
    """
    print("=== 测试正方形检测功能 ===")
    
    # 初始化检测器
    detector = SquareDetector()
    
    # 测试图像列表
    test_images = ["test_rectangle.jpg", "test_rectangle_realistic.jpg"]
    
    for img_path in test_images:
        print(f"\n测试图像: {img_path}")
        
        try:
            # 读取测试图像
            frame = cv2.imread(img_path)
            if frame is None:
                print(f"❌ 无法读取图像: {img_path}")
                continue
                
            print(f"✅ 图像读取成功，尺寸: {frame.shape[1]}x{frame.shape[0]}")
            
            # 尝试标定
            result = detector.calibrate_square(frame)
            
            if result:
                print("✅ 矩形检测成功！")

                # 获取检测结果
                calibrated_data = detector.get_calibrated_data()
                print(f"矩形边界: {calibrated_data['square_bounds']}")
                print(f"中心点坐标: {calibrated_data['center_point']}")
                print(f"矩形顶点数量: {len(calibrated_data['square_vertices'])}")

                # 绘制检测结果
                result_frame = draw_detection_result(frame, calibrated_data)
                result_filename = f"detection_result_{img_path}"
                cv2.imwrite(result_filename, result_frame)
                print(f"检测结果已保存: {result_filename}")

            else:
                print("❌ 矩形检测失败")

                # 尝试调整参数
                print("尝试调整检测参数...")

                # 调整边框颜色范围
                detector.set_frame_color_range(
                    np.array([0, 0, 0]),     # 黑色下限
                    np.array([180, 255, 60]) # 黑色上限
                )

                # 调整内部白色范围
                detector.set_inner_color_range(
                    np.array([0, 0, 180]),   # 白色下限
                    np.array([180, 50, 255]) # 白色上限
                )

                # 再次尝试
                result = detector.calibrate_square(frame)
                if result:
                    print("✅ 调整参数后检测成功！")
                    calibrated_data = detector.get_calibrated_data()
                    print(f"矩形边界: {calibrated_data['square_bounds']}")
                    print(f"中心点坐标: {calibrated_data['center_point']}")
                else:
                    print("❌ 调整参数后仍然检测失败")

                    # 显示调试信息
                    debug_detection(frame, detector)
            
            # 重置检测器状态
            detector.reset_calibration()
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()

def debug_detection(frame, detector):
    """
    调试检测过程，保存中间结果
    """
    print("保存调试图像...")
    
    # 转换到HSV
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # 创建边框掩码和内部掩码
    frame_mask = cv2.inRange(hsv, detector.frame_hsv_lower, detector.frame_hsv_upper)
    inner_mask = cv2.inRange(hsv, detector.inner_hsv_lower, detector.inner_hsv_upper)
    
    # 保存调试图像
    cv2.imwrite("debug_hsv.jpg", hsv)
    cv2.imwrite("debug_frame_mask.jpg", frame_mask)
    cv2.imwrite("debug_inner_mask.jpg", inner_mask)

    print("调试图像已保存: debug_hsv.jpg, debug_frame_mask.jpg, debug_inner_mask.jpg")

    # 分析掩码
    frame_pixels = np.sum(frame_mask == 255)
    inner_pixels = np.sum(inner_mask == 255)
    total_pixels = frame_mask.shape[0] * frame_mask.shape[1]
    print(f"边框掩码中白色像素: {frame_pixels}/{total_pixels} ({frame_pixels/total_pixels*100:.2f}%)")
    print(f"内部掩码中白色像素: {inner_pixels}/{total_pixels} ({inner_pixels/total_pixels*100:.2f}%)")

def draw_detection_result(frame, calibrated_data):
    """
    绘制检测结果
    """
    result_frame = frame.copy()
    
    # 绘制正方形边界 (绿色实线)
    if calibrated_data['square_vertices'] is not None:
        vertices = calibrated_data['square_vertices']
        cv2.polylines(result_frame, [vertices], True, (0, 255, 0), 3)
        
        # 绘制顶点
        for i, vertex in enumerate(vertices):
            cv2.circle(result_frame, tuple(vertex), 5, (255, 0, 0), -1)
            cv2.putText(result_frame, str(i), tuple(vertex + 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
    
    # 绘制中心点 (红色圆点和十字)
    if calibrated_data['center_point'] is not None:
        cx, cy = calibrated_data['center_point']
        
        # 绘制红色中心点
        cv2.circle(result_frame, (cx, cy), 8, (0, 0, 255), -1)
        cv2.circle(result_frame, (cx, cy), 12, (0, 0, 255), 2)
        
        # 绘制十字标记
        cross_len = 20
        cv2.line(result_frame, (cx - cross_len, cy), (cx + cross_len, cy), (0, 0, 0), 3)
        cv2.line(result_frame, (cx, cy - cross_len), (cx, cy + cross_len), (0, 0, 0), 3)
        
        # 添加坐标文字
        coord_text = f"({cx}, {cy})"
        cv2.putText(result_frame, coord_text, (cx + 25, cy), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(result_frame, coord_text, (cx + 25, cy), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
    
    return result_frame

if __name__ == "__main__":
    test_square_detection()
